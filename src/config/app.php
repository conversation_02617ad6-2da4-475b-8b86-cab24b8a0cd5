<?php

return [

    'name' =>  env('APP_NAME', 'Newswav-API-v3'),
    'env' =>  env('APP_ENV', 'local'),
    'debug' =>  env('APP_DEBUG', true),
    'url' =>  env('APP_URL', 'http://localhost'),
    'api_url' =>  env('API_URL', 'http://localhost'),
    'website_url' => env('WEBSITE_URL', 'http://localhost'),
    'rss_feed_url' => env('WEBSITE_URL', 'http://localhost') . "/rss/",
    'election_service_url' => env('ELECTION_SERVICE_URL', 'http://localhost/'),
    'timezone' => env('APP_TIMEZONE', 'Asia/Kuala_Lumpur') ,
    'asset_url' => env('ASSET_URL', null),
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'jwt_key' =>  env('JWT_KEY', null),
    'corona-key' => env('CORONA-CARD-API-KEY'),
    'key' => env('APP_KEY'),
    'cipher' =>  'AES-256-CBC',
    'fcm_token' => env('FCM_TOKEN', false),
    'launch_darkly_sdk' => env('LAUNCH_DARKLY_SDK_KEY', ''),
    'launch_darkly_url' => env('LAUNCH_DARKLY_URL', ''),
    'slack_socmed_channel_webhook' => env('SLACK_SOCMED_CHANNEL_WEBHOOK'),
    'slack_backend_notify_channel_webhook' => env('SLACK_BACKEND_NOTIFY_WEBHOOK'),
    'aliases' => [

        'Excel' => Maatwebsite\Excel\Facades\Excel::class,
        'App' => Illuminate\Support\Facades\App::class,
        'Artisan' => Illuminate\Support\Facades\Artisan::class,
        'Auth' => Illuminate\Support\Facades\Auth::class,
        'Blade' => Illuminate\Support\Facades\Blade::class,
        'Broadcast' => Illuminate\Support\Facades\Broadcast::class,
        'Bus' => Illuminate\Support\Facades\Bus::class,
        'Cache' => Illuminate\Support\Facades\Cache::class,
        'Config' => Illuminate\Support\Facades\Config::class,
        'Cookie' => Illuminate\Support\Facades\Cookie::class,
        'Crypt' => Illuminate\Support\Facades\Crypt::class,
        'DB' => Illuminate\Support\Facades\DB::class,
        'Eloquent' => Illuminate\Database\Eloquent\Model::class,
        'Event' => Illuminate\Support\Facades\Event::class,
        'File' => Illuminate\Support\Facades\File::class,
        'Gate' => Illuminate\Support\Facades\Gate::class,
        'Hash' => Illuminate\Support\Facades\Hash::class,
        'Lang' => Illuminate\Support\Facades\Lang::class,
        'Log' => Illuminate\Support\Facades\Log::class,
        'Mail' => Illuminate\Support\Facades\Mail::class,
        'Notification' => Illuminate\Support\Facades\Notification::class,
        'Password' => Illuminate\Support\Facades\Password::class,
        'Queue' => Illuminate\Support\Facades\Queue::class,
        'Redirect' => Illuminate\Support\Facades\Redirect::class,
        'Redis' => Illuminate\Support\Facades\Redis::class,
        'Request' => Illuminate\Support\Facades\Request::class,
        'Response' => Illuminate\Support\Facades\Response::class,
        'Route' => Illuminate\Support\Facades\Route::class,
        'Schema' => Illuminate\Support\Facades\Schema::class,
        'Session' => Illuminate\Support\Facades\Session::class,
        'Storage' => Illuminate\Support\Facades\Storage::class,
        'URL' => Illuminate\Support\Facades\URL::class,
        'Validator' => Illuminate\Support\Facades\Validator::class,
        'View' => Illuminate\Support\Facades\View::class,
        'Newsletter' => Spatie\Newsletter\NewsletterFacade::class,

    ],
    'delayed_insert' => env('DELAYED_TRACK_EVENTS_INSERT', true),
    'no_poll' => env('NO_POLL', false),
    'poll_pos' => env('POLL_POS', 3),
    'bookmark_flag' => env('SHOW_BOOKMARK_FLAG', false),
    'history_flag' => env('SHOW_HISTORY_FLAG', false),
    'image_proxy_key' => env('IMAGE_PROXY_KEY', ""),
    'image_proxy_quality' => env('IMAGE_PROXY_QUALITY', 50),
    'image_proxy_square' => env('IMAGE_PROXY_SQUARE', 500),
    'image_proxy_width' => env('IMAGE_PROXY_WIDTH', 800),
    'image_proxy_height' => env('IMAGE_PROXY_HEIGHT', 450),
    'current_year' => date("Y"),
    'bunker_override' => env('BUNKER_OVERRIDE', null)
];
