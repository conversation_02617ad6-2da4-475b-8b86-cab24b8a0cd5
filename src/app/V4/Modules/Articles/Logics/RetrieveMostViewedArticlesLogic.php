<?php

namespace App\V4\Modules\Articles\Logics;

use App\V4\Repositories\ArticleRepository;
use Illuminate\Http\JsonResponse;

class RetrieveMostViewedArticlesLogic
{
    private ArticleRepository $articleRepository;

    public function __construct(
        ArticleRepository $articleRepository
    )
    {
        $this->articleRepository = $articleRepository;
    }
    public function execute(string $fromDate, string $toDate, ?array $languages, ?string $uniqueId, ?array $projects, int $page): JsonResponse
    {
        $data = $this->articleRepository->getMostViewedArticles($fromDate, $toDate, $languages, $uniqueId, $projects, $page);
        return response()->json($data);
    }
}