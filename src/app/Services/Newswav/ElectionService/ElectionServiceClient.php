<?php

namespace App\Services\Newswav\ElectionService;

use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;

class ElectionServiceClient
{
    private Client $client;

    public function __construct(Client $client) {
        $this->client = $client;
    }

    public function recreateCache(int $electionId): void {
        $this->makeRequest(
            sprintf('api/internal/election/%s/cache/recreate', $electionId),
            'POST'
        );
    }

    private function makeRequest(string $endpoint, string $method, array $parameters = []): ResponseInterface {
        $endpointUrl = sprintf('%s/%s', config('app.election_service_url'), $endpoint);

        $response =  $this->client->request($method, $endpointUrl, [
            'headers' => [
                'Authorization' => sprintf('Bearer %s', config('secrets.election_api_secret')),
                'Content-Type'  => 'application/json',
            ],
            'json' => $parameters,
        ]);

        return $response;
    }
}
