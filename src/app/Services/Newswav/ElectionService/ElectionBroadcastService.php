<?php

namespace App\Services\Newswav\ElectionService;

use Illuminate\Support\Facades\Log;
use Pusher\Pusher;

class ElectionBroadcastService
{
    /**
     * @var Pusher
     */
    protected $pusher;

    /**
     * The Pusher event name
     *
     * @var string
     */
    const CHANNEL_PREFIX = 'election-';

    const EVENT_NAME = 'election-updated';

    /**
     * Create a new ElectionBroadcastService instance.
     */
    public function __construct()
    {
        $this->pusher = new Pusher(
            env('PUSHER_APP_KEY'),
            env('PUSHER_APP_SECRET'),
            env('PUSHER_APP_ID'),
            [
                'cluster' => 'ap1',
                'useTLS' => true,
                'host' => env('PUSHER_HOST'),
                'port' => 443,
                'scheme' => 'https',
                'encrypted' => true,
            ]
        );
    }

    /**
     * Broadcast election update to Pusher
     *
     * @param array $data
     * @return bool
     */
    public function broadcastUpdate(string $electionCode, array $data = []): bool
    {
        $channel = sprintf('%s%s', self::CHANNEL_PREFIX, $electionCode);

        try {
            $this->pusher->trigger($channel, self::EVENT_NAME, $data);

            Log::info('Election update broadcasted successfully', [
                'channel' => $channel,
                'event' => self::EVENT_NAME,
                'data' => $data
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast election update', [
                'channel' => $channel,
                'event' => self::EVENT_NAME,
                'data' => $data
            ]);

            return false;
        }
    }
}

