<?php

namespace App\Events;

use App\V4\Common\Constants\CustomUserTrackingActions;
use Illuminate\Queue\SerializesModels;

class CustomUserTrackingEvent
{
    use SerializesModels;

    private string $action;
    private array $metadata;
    private string $key;
    private string $userProfileId;

    public function __construct(
        string $userProfileId,
        string $action,
        string $key,
        array $metadata
    )
    {
        $this->userProfileId = $userProfileId;
        $this->action = $action;
        $this->metadata = $metadata;
        $this->key = $key;
    }

    public function getUserProfileId(): string
    {
        return $this->userProfileId;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public static function makeHighlightUserTrackingEvent(string $profileId, string $highlightSlug): self {
        return new self(
            $profileId, CustomUserTrackingActions::HIGHLIGHT_PAGE_VISITED, sprintf('highlight_%s', $highlightSlug), ['highlight_slug' => $highlightSlug]
        );
    }

    public static function makePollVoteUserTrackingEvent(string $profileId, int $pollId, int $answerId, bool $vote): self {
        return new self(
            $profileId, CustomUserTrackingActions::POLL_VOTED, sprintf('poll_%s', $pollId), [
                'poll_id' => $pollId,
                'answer_id' => $answerId,
                'vote' => $vote,
            ]
        );
    }
}
