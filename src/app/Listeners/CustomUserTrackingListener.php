<?php

namespace App\Listeners;

use App\Events\CustomUserTrackingEvent;
use App\V4\Models\CustomUserTrackingEvent as CustomUserTrackingEventModel;

class CustomUserTrackingListener
{
    public function handle(CustomUserTrackingEvent $event): void
    {
        CustomUserTrackingEventModel::create([
            'action' => $event->getAction(),
            'user' => [
                'profile_id' => $event->getUserProfileId(),
            ],
            'key' => $event->getKey(),
            'metadata' => $event->getMetadata(),
        ]);

    }
}
