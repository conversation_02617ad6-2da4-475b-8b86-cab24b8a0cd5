<?php

namespace App\Http\Controllers;

use App\Http\Requests\AddPublisherToExploreRequest;
use App\Http\Requests\Admin\AdminRequest;
use App\V4\Http\Requests\PN2_0\AiPnConfigurationsRequestV4;
use App\Http\Requests\Admin\BanCommentUserRequest;
use App\Http\Requests\Admin\VerifyUserRequest;
use App\Http\Requests\Admin\CreatePNChannelRequest;
use App\Http\Requests\Admin\DefaultFollowListRequest;
use App\Http\Requests\AdminGetUsersRequest;
use App\Http\Requests\ChannelsViewModeRequest;
use App\Http\Requests\HideArticleRequest;
use App\Http\Requests\Admin\AllContentRequest;
use App\Http\Requests\SendPn4Request;
use App\Modules\Media\Services\CreatesMedia;
use App\Repositories\Media\MediaRepository;
use App\Services\Newswav\ScheduledPn\ScheduledPnService;
use function GuzzleHttp\json_encode;
use App\V4\Common\Constants;
use App\Http\Requests\Admin\ListPublisherRequest;
use App\Http\Requests\Admin\CreateVideoFbPublisherRequest;
use App\Http\Requests\Admin\CreatePublisherRequest;
use App\Http\Requests\Admin\EnablePublisherAndChannelsRequest;
use App\Http\Requests\Admin\ToggleArticleFeedRequest;
use App\Http\Requests\Admin\UpdateCoronaArticleRequest;
use App\Http\Requests\Admin\CommentKeywordRequest;
use App\Http\Requests\Admin\MostViewedArticlesRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Google\Cloud\Storage\StorageClient;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\FCMPushNotification;
use App\Http\Requests\Admin\AnnouncementRequest;
use App\Http\Requests\Admin\HighlightsRequest;
use App\Http\Requests\Admin\SendFcmRequest;
use App\Http\Requests\Admin\SendFcmRequest2;
use App\Http\Requests\Admin\PinRequest;
use App\Http\Requests\Admin\PinRequest2;
use App\Http\Requests\Admin\PnCancelRequest;
use App\Repositories\Announcement\AnnouncementRepository;
use App\Repositories\Publisher\PublisherRepository;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use App\Repositories\Post\PostRepository;
use App\Repositories\Article\ArticleRepository;
use App\Repositories\Highlights\HighlightsRepository;
use App\Repositories\Podcast\PodcastRepository;
use App\Repositories\Report\ReportRepository;
use App\Repositories\Topic\TopicRepository;
use App\Repositories\Video\VideoRepository;
use App\Services\Newswav\Pin\PinItemService;
use Exception;
use Illuminate\Http\Request;
use voku\helper\HtmlDomParser;
use Newswav\NewswavAuth\Facade\NewswavAuth;
use App\Services\Newswav\Monitoring\MonitoringService;
use App\Http\Requests\Monitoring\MonitoringRequest;
use App\Events\AdminUpdatePublisherEvent;
use App\Events\AdminPinContentEvent;
use App\Events\AdminUnpinContentEvent;
use App\Services\Revenue\RevenueService;
use App\Events\AdminChangeContentStatusEvent;
use App\Repositories\Feed\FeedRepository;
use App\Events\CovidContentUpdatedEvent;
use App\V4\Modules\Translations\Logics\TranslateArticlesLogic;
use App\V4\Modules\PN2_0\Logics\GetAiPnConfigurationLogic;
use App\V4\Modules\PN2_0\Logics\CreateAiPnConfigurationLogic;
use App\V4\Modules\PN2_0\Logics\UpdateAiPnConfigurationLogic;
use App\V4\Modules\PN2_0\Logics\DeleteAiPnConfigurationLogic;
use App\V4\Modules\PN2_0\Logics\GetPNTitleAndDescriptionSuggestionLogic;
use App\V4\Modules\SlackChannels\Logics\RetrieveSlackChannelsLogic;
use App\Services\Newswav\Content\ContentService;
use App\V4\Services\UGC\ClearCacheTrait;
use App\V4\Modules\Articles\Logics\RetrieveMostViewedArticlesLogic;
use Illuminate\Http\JsonResponse;
use App\Services\Newswav\ElectionService\ElectionServiceClient;
use App\Services\Newswav\ElectionService\ElectionBroadcastService;

class AdminController extends Controller
{

    use ClearCacheTrait;

    public function updateCoronaArticle(UpdateCoronaArticleRequest $request)
    {
        $totalConfirmed = $request->input('totalConfirmed');
        $newConfirmed = $request->input('todayConfirmed') ?? 0;
        $totalRecovered = $request->input('totalRecovered');
        $newRecovered = $request->input('todayRecovered') ?? 0;
        $totalDeath =  $request->input('totalDeath');
        $newDeath = $request->input('todayDeath') ?? 0;

        if ($totalConfirmed < 8000 || $totalRecovered < 8000 || $totalDeath  < 100)
            abort(422, "total numbers wrong");


        $recoveredRate = ($totalRecovered / $totalConfirmed) * 100;
        $recoveredRate = number_format((float)$recoveredRate, 2, '.', '');
        $adddate = Carbon::now()->format('Y-m-d H:i:s');


        $title = Carbon::now()->format('d F') . ' COVID-19 Cases Update';
        $today = Carbon::now()->format('F d');
        $description = $newConfirmed . ' confirmed cases.';
        $html =
            <<<BQ
        <p>KUALA LUMPUR, $today  — The Ministry of Health (MoH) announced today that $newConfirmed new cases recorded today, making the cumulative total of those confirmed case at $totalConfirmed cases</p>
        <p>$newRecovered Covid-19 patients have recovered and been discharged today, making the total discharged patients at $totalRecovered cases.</p>
        <p>$newDeath new death were registered, resulting a total death toll of $totalDeath.</p>
        <p>Current recovered rate is at $recoveredRate%.</p>
        <p>Total Confirmed : $totalConfirmed</p>
        <p>Today Confirmed : $newConfirmed</p>
        <p>Total Recovered : $totalRecovered</p>
        <p>Today Recovered : $newRecovered</p>
        <p>Total Death : $totalDeath</p>
        <p>Today Death : $newDeath</p>
        <p>More to follow...</p>
        BQ;



        DB::table('article')->where('uniqueID', 'covid19__en')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html
        ]);

        //ZH
        $title = Carbon::now()->format('d') . '日新冠肺炎数据';
        $today = Carbon::now()->format('d') . '';
        $description = $newConfirmed . ' 新确诊.';
        $html = <<<BQ
        <p>(布城 $today 日讯）截至今日中午12时，我国再新增 $newConfirmed 宗新冠肺炎确诊病例，累积确诊病例达 $totalConfirmed 宗.</p>
        <p>今日痊愈出院的有 $newRecovered 人，累积出院的患者有 $totalRecovered 人.</p>
        <p>$newDeath 宗新冠肺炎死亡病例，累积死亡病例已经达到 $totalDeath 宗.</p>
        <p>今天总康复率为 $recoveredRate%.</p>
        <p>总确诊人数 : $totalConfirmed</p>
        <p>今天确诊人数 : $newConfirmed</p>
        <p>总康复人数 : $totalRecovered</p>
        <p>今天康复人数 : $newRecovered</p>
        <p>总死亡人数 : $totalDeath</p>
        <p>今天死亡人数 : $newDeath</p>
        <p>更多更新...</p>
        BQ;

        DB::table('article')->where('uniqueID', 'covid19__zh')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html
        ]);


        $title = Carbon::now()->format('d F') . ' Kes COVID-19';
        $today = Carbon::now()->format('d F');
        $description = $newConfirmed . ' kes baru.';
        $html = <<<BQ
        <p>KUALA LUMPUR : Malaysia mencatatkan $newConfirmed kes positif COVID-19, jam 12 tengahari menjadikan jumlah kumulatif adalah $totalConfirmed kes.</p>
        <p>Sehingga kini, seramai $totalRecovered kes pesakit telah dirawat dan dilepaskan dari hospital, $newRecovered dilepask hari ini.</p>
        <p>$newDeath kematian dicatatkan menjadikanb jumlah kumulatif kematian adalah sebanyak $totalDeath kes.</p>
        <p>Kadar kepulihan sehingga hari ini adalah $recoveredRate%</p>
        <p>Jumlah Kes Positive: $totalConfirmed</p>
        <p>Kes Positive Hari Ini : $newConfirmed</p>
        <p>Jumlah Kes Sembuh : $totalRecovered</p>
        <p>Kes Sembuh Hari Ini : $newRecovered</p>
        <p>Jumlah Kes Maut : $totalDeath</p>
        <p>Kes Maut Hari Ini : $newDeath</p>
        <p>Sedang dikemaskini...</p>
        BQ;

        DB::table('article')->where('uniqueID', 'covid19__ms')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html
        ]);

        sleep(1);
        Cache::forget("a_covid19__en_2");
        Cache::forget("a_covid19__ms_2");
        Cache::forget("a_covid19__zh_2");
        return  response()->json(["message" => "SUCCESS, GOOD TO GO"]);
    }



    public function updateCoronaArticleV2(UpdateCoronaArticleRequest $req)
    {
        $totalConfirmed = $req->input('totalConfirmed');
        $newConfirmed = $req->input('todayConfirmed') ?? 0;
        $totalRecovered = $req->input('totalRecovered');
        $newRecovered = $req->input('todayRecovered') ?? 0;
        $totalDeath =  $req->input('totalDeath');
        $newDeath = $req->input('todayDeath') ?? 0;
        $customEnInput = $req->input('enInput') ?? "";
        $customMsInput = $req->input('msInput') ?? "";
        $customZhInput = $req->input('zhInput') ?? "";

        $img1 = $req->input('img1');
        $img2 = $req->input('img2');
        $img3 = $req->input('img3');
        $img4 = $req->input('img4');


        $img1Link = !$img1 || empty($img1) ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img1);
        $img2Link = !$img2 || empty($img2)  ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img2);
        $img3Link = !$img3 || empty($img3)  ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img3);
        $img4Link = !$img4 || empty($img4) ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img4);

        $img1Tag =  $img1 ? "<p><img  " . 'onClick="' . "showImageDetail('0')" . '" ' . "src=\"$img1Link\"/></p>" : "<p></p>";
        $img2Tag =  $img2 ? "<p><img  " . 'onClick="' . "showImageDetail('1')" . '" ' . "src=\"$img2Link\"/></p>" : "<p></p>";
        $img3Tag =  $img3 ? "<p><img  " . 'onClick="' . "showImageDetail('2')" . '" ' . "src=\"$img3Link\"/></p>" : "<p></p>";
        $img4Tag =  $img4 ? "<p><img  " . 'onClick="' . "showImageDetail('3')" . '" ' . "src=\"$img4Link\"/></p>" : "<p></p>";


        $mediaIds = '6985713';
        /** @var CreatesMedia $createsMedia */
        $createsMedia = app(CreatesMedia::class);

        if ($img1Link) {
            $id = $createsMedia->execute($img1Link);
            $mediaIds = $id . "," . $mediaIds;
        }

        if ($img2Link) {
            $id = $createsMedia->execute($img2Link);
            $mediaIds = $id . "," . $mediaIds;
        }

        if ($img3Link) {
            $id = $createsMedia->execute($img3Link);
            $mediaIds = $id . "," . $mediaIds;
        }

        if ($img4Link) {
            $id = $createsMedia->execute($img4Link);
            $mediaIds = $id . "," . $mediaIds;
        }

        if ($totalConfirmed < 8000 || $totalRecovered < 8000 || $totalDeath  < 100)
            abort(422, "total numbers wrong");


        $rows = [];
        $rows[] = [
            'country' => "Malaysia",
            'city' => 0,
            'suspected' =>  0,
            'confirmed' => $totalConfirmed,
            'cured' => $totalRecovered,
            'deaths' => $totalDeath,
            'date' => time(),
        ];
        $this->saveToDb($rows, "corona_stats_2");

        $recoveredRate = ($totalRecovered / $totalConfirmed) * 100;
        $recoveredRate = number_format((float)$recoveredRate, 2, '.', '');
        $adddate = Carbon::now()->format('Y-m-d H:i:s');


        $title = Carbon::now()->format('d F') . ' COVID-19 Cases Update';
        $today = Carbon::now()->format('F d');
        $description = $newConfirmed . ' confirmed cases.';

        $newConfirmed = number_format((float)$newConfirmed);
        $totalConfirmed = number_format((float)$totalConfirmed);
        $newRecovered = number_format((float)$newRecovered);
        $totalRecovered = number_format((float)$totalRecovered);
        $newDeath = number_format((float)$newDeath);
        $totalDeath = number_format((float)$totalDeath);

        $html =
            <<<BQ
        <p>KUALA LUMPUR, <strong>$today</strong> — The Ministry of Health (MoH) announced that there were a total of <strong>$newConfirmed</strong> new cases reported up till 12pm today, pushing the cumulative figure for positive cases in Malaysia to <strong>$totalConfirmed</strong>.</p>
        <p>The ministry also reported that <strong>$newRecovered</strong> patients had recovered and discharged today, bringing the cumulative figure for recoveries to <strong>$totalRecovered</strong> so far.</p>
        <p>Unfortunately, <strong>$newDeath</strong> new deaths were registered today, resulting in a total death toll of <strong>$totalDeath</strong>.</p>
        <p>$customEnInput</p>
        <p>Current recovery rate is at <strong>$recoveredRate%</strong></p>
        <p>Today Confirmed: <strong>$newConfirmed</strong></p>
        <p>Total Confirmed: <strong>$totalConfirmed</strong></p>
        <p>Today Recovered: <strong>$newRecovered</strong></p>
        <p>Total Recovered: <strong>$totalRecovered</strong></p>
        <p>Today Deaths: <strong>$newDeath</strong></p>
        <p>Total Deaths: <strong>$totalDeath</strong></p>
        $img1Tag
        $img2Tag
        $img3Tag
        $img4Tag
        <p>More to follow...</p>
        BQ;



        DB::table('article')->where('uniqueID', 'covid19__en')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html,
            'media' => $mediaIds
        ]);

        //ZH
        $title = Carbon::now()->format('d') . '日新冠肺炎数据';
        $today = Carbon::now()->format('d') . '';
        $description = $newConfirmed . ' 新确诊.';
        $html = <<<BQ
        <p>(吉隆坡，<strong>$today</strong>日讯）截至今日中午12时，我国新增 <strong>$newConfirmed</strong> 宗新冠肺炎确诊病例，目前累积确诊病例达 <strong>$totalConfirmed</strong>宗。</p>
        <p>我国今日共有 <strong>$newRecovered</strong>人康复出院，迄今累积康复人数达 <strong>$totalRecovered</strong>人。</p>
        <p>同时，我国另添 <strong>$newDeath</strong> 宗死亡病例，至今累积死亡人数增至 <strong>$totalDeath</strong>人。</p>
        <p>$customZhInput</p>
        <p>截今出院康复率为 <strong>$recoveredRate%</strong></p>
        <p>新增确诊: <strong>$newConfirmed</strong></p>
        <p>累计确诊: <strong>$totalConfirmed</strong></p>
        <p>新增康复: <strong>$newRecovered</strong></p>
        <p>累计康复: <strong>$totalRecovered</strong></p>
        <p>新增死亡: <strong>$newDeath</strong></p>
        <p>累计死亡: <strong>$totalDeath</strong></p>
        $img1Tag
        $img2Tag
        $img3Tag
        $img4Tag
        <p>更新中...</p>
        BQ;

        DB::table('article')->where('uniqueID', 'covid19__zh')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html,
            'media' => $mediaIds
        ]);


        $title = Carbon::now()->format('d F') . ' Kes COVID-19';
        $today = Carbon::now()->format('d F');
        $description = $newConfirmed . ' kes baru.';
        $html = <<<BQ
        <p>KUALA LUMPUR, <strong>$today</strong>: Setakat jam 12 tengah hari, Malaysia mencatatkan <strong>$newConfirmed</strong> kes baharu COVID-19, menjadikan jumlah kumulatif kes positif di Malaysia <strong>$totalConfirmed</strong> kes.</p>
        <p><strong>$newRecovered</strong> kes sembuh dilaporkan hari ini dan jumlah kumulatif kes sembuh adalah sebanyak <strong>$totalRecovered</strong> kes. Kadar kepulihan setakat ini adalah <strong>$recoveredRate%</strong></p>
        <p>Dukacita dimaklumkan bahawa terdapat <strong>$newDeath</strong> kes kematian direkodkan hari ini, menjadikan jumlah kumulatif kes maut <strong>$totalDeath</strong> kes.</p>
        <p>$customMsInput</p>
        <p>Kes Positif Hari Ini: <strong>$newConfirmed</strong></p>
        <p>Jumlah Kumulatif Kes Positif: <strong>$totalConfirmed</strong></p>
        <p>Kes Sembuh Hari Ini: <strong>$newRecovered</strong></p>
        <p>Jumlah Kumulatif Kes Sembuh: <strong>$totalRecovered</strong></p>
        <p>Kes Maut Hari Ini: <strong>$newDeath</strong></p>
        <p>Jumlah Kumulatif Kes Maut: <strong>$totalDeath</strong></p>
        $img1Tag
        $img2Tag
        $img3Tag
        $img4Tag
        <p>Akan menyusul...</p>
        BQ;

        DB::table('article')->where('uniqueID', 'covid19__ms')->update([
            'title' => $title,
            'description' => $description,
            'publishedDate' => $adddate,
            'html' => $html,
            'media' => $mediaIds
        ]);

        sleep(1);
        Cache::forget("a_covid19__en_2");
        Cache::forget("a_covid19__ms_2");
        Cache::forget("a_covid19__zh_2");
        return  response($html);
    }

    public function updateCoronaArticleV3(UpdateCoronaArticleRequest $req)
    {
        // temporarily enable for dev env so that we have a fallback in production
        // to remove when this merges into master
        $dryMode = app()->env != "production";
        $dateToUse = $req->input('dateToUse') ?? Carbon::now()->format('Y-m-d H:i:s');
        $newConfirmed = (int)($req->input('todayConfirmed') ?? -1);
        $newRecovered = (int)($req->input('todayRecovered') ?? -1);
        $newDeath = (int)($req->input('todayDeath') ?? -1);
        $newActive = (int)($req->input('todayActive') ?? -1);
        $newBID = (int)($req->input('todayBID') ?? -1);

        $totalConfirmed = (int)trim($req->input('totalConfirmed'));
        $totalRecovered = (int)trim($req->input('totalRecovered'));
        $totalDeath =  (int)trim($req->input('totalDeath'));
        $customEnInput = $req->input('enInput') ?? "";
        $customMsInput = $req->input('msInput') ?? "";
        $customZhInput = $req->input('zhInput') ?? "";
        $customInputDraft = $req->input('customInputDraft') ?? null;

        $shouldUpdatePublishedDate = $req->input('shouldUpdatePublishedDate') == 'true' ?? false;

        // get yesterday data to populate in article
        $latestCovidData = $this->getCovidData('Malaysia', Carbon::now()->subDays(1));
        $totalActive = $latestCovidData->active ?? -1;
        $totalBID = $latestCovidData->bid ?? -1;

        $img1 = $req->input('img1');
        $img2 = $req->input('img2');
        $img3 = $req->input('img3');
        $img4 = $req->input('img4');


        $uploadedImages = $this->getUploadedCovidImagesAtributes($img1, $img2, $img3, $img4);

        $headerImg = $uploadedImages->headerImg;
        $mediaIds = $uploadedImages->mediaIds;
        $htmlGallery = $uploadedImages->htmlGallery;

        if ($totalConfirmed < 8000 || $totalRecovered < 8000 || $totalDeath  < 100)
            abort(422, "total numbers wrong");


        $rows = [];
        $rows[] = [
            'country' => "Malaysia",
            'city' => 0,
            'suspected' =>  0,
            'confirmed' => $totalConfirmed,
            'cured' => $totalRecovered,
            'deaths' => $totalDeath,
            'date' => time(),
        ];

        if (!$dryMode) {
            $this->saveToDb($rows, "corona_stats_2");
        }

        // update history table
        $updatedData = [
            'totalConfirmed'    => $totalConfirmed,
            'todayConfirmed'    => $newConfirmed,
            'totalRecovered'    => $totalRecovered,
            'todayRecovered'    => $newRecovered,
            'totalDeath'        => $totalDeath,
            'todayDeath'        => $newDeath,
            'todayActive'       => $newActive,
            'todayBID'          => $newBID,
            'enInput'           => $customEnInput,
            'zhInput'           => $customZhInput,
            'msInput'           => $customMsInput,
            'customInputDraft'  => $customInputDraft,
        ];

        $updatedImageData = [
            'img1' =>  $img1 ?? '',
            'img2' =>  $img2 ?? '',
            'img3' =>  $img3 ?? '',
            'img4' =>  $img4 ?? ''
        ];

        $this->updateCovidArticleHistory('article', $updatedData);
        $this->updateCovidArticleHistory('image', $updatedImageData);

        $recoveredRate = ($totalRecovered / $totalConfirmed) * 100;
        $recoveredRate = number_format((float)$recoveredRate, 2, '.', '');

        // get existing published date from EN article
        $existingPubDate = DB::table('article')->where('uniqueID', 'covid19__en')->first();
        $carbonToday = ($existingPubDate && $existingPubDate->publishedDate) ? Carbon::parse($existingPubDate->publishedDate) : Carbon::now();
        $toUpdate = [];
        if ($shouldUpdatePublishedDate) {
            $carbonToday = Carbon::now();
            $toUpdate['publishedDate'] = $carbonToday->format('Y-m-d H:i:s');
        }

        $dateToUse = Carbon::parse($dateToUse);

        $title = $dateToUse->format('d F') . ' COVID-19 Cases Update';
        $today = $carbonToday->format('F d');
        $description = ((int)$newConfirmed) > -1 ?  $newConfirmed . ' confirmed cases.' : "";
        $newConfirmed = number_format((float)$newConfirmed);
        $totalConfirmed = number_format((float)$totalConfirmed);
        $newRecovered = number_format((float)$newRecovered);
        $totalRecovered = number_format((float)$totalRecovered);
        $newDeath = number_format((float)$newDeath);
        $totalDeath = number_format((float)$totalDeath);
        $dataAsOf = $carbonToday->format('d M Y, h:i a'); // article update from non-crawler will use current time
        // -0 = reset stat
        $newActive = $newActive !== '-0' ? number_format((float)$newActive) : null;
        $totalActive = $totalActive > -1 ? number_format((float)$totalActive) : '-';
        $newBID = number_format((float)$newBID);
        $totalBID = $totalBID > -1 ? number_format((float)$totalBID) : '-';

        $deathsEn = "";
        $deathsMs = "";
        $deathsZh = "";

        $recoveredEn = "";
        $recoveredMs = "";
        $recoveredZh = "";

        $confirmedEn = "";
        $confirmedMs = "";
        $confirmedZh = "";

        $todayConfirmedEn = "<p>Today Confirmed: <strong>$newConfirmed</strong></p>";
        $todayConfirmedMs = "<p>Kes Positif Hari Ini: <strong>$newConfirmed</strong></p>";
        $todayConfirmedZh = "<p>新增确诊: <strong>$newConfirmed</strong></p>";

        $todayRecoveredEn = "<p>Today Recovered: <strong>$newRecovered</strong></p>";
        $todayRecoveredMs = "<p>Kes Sembuh Hari Ini: <strong>$newRecovered</strong></p>";
        $todayRecoveredZh = "<p>新增康复: <strong>$newRecovered</strong></p>";

        $todayDeathsEn = "<p>Today Deaths: <strong>$newDeath</strong></p>";
        $todayDeathsMs = "<p>Kes Maut Hari Ini: <strong>$newDeath</strong></p>";
        $todayDeathsZh = "<p>新增死亡: <strong>$newDeath</strong></p>";

        // take note that below copywriting is maintained in covid crawler as well
        if (((int)$newDeath) == 0) {
            $deathsEn = "Fortunately, <strong>no new death</strong> was registered today, keeping the death toll at <strong>$totalDeath</strong>.";
            $deathsMs = "Sukacita dimaklumkan bahawa <strong>tiada kes kematian</strong> yang direkodkan hari ini, menjadikan jumlah kes maut masih berada di <strong>$totalDeath</strong> kes.";
            $deathsZh = "确幸的是，今日<strong>没有新的死亡记录</strong>，死亡人数仍保持在<strong>$totalDeath</strong>。";
        } else if (((int)$newDeath) > 0) {
            $deathsEn = "Unfortunately, <strong>$newDeath</strong> new deaths were registered, resulting in a total death toll of <strong>$totalDeath</strong>.";
            $deathsMs = "Dukacita dimaklumkan bahawa terdapat <strong>$newDeath</strong> kes kematian direkodkan, menjadikan jumlah kumulatif kes maut <strong>$totalDeath</strong> kes.";
            $deathsZh = "同时，我国另添 <strong>$newDeath</strong> 宗死亡病例，至今累积死亡人数增至 <strong>$totalDeath</strong>人。";
        } else {
            $todayDeathsEn = "";
            $todayDeathsMs = "";
            $todayDeathsZh = "";
        }


        if (((int)$newRecovered) == 0) {
            $recoveredEn = "The ministry also reported <strong>0 recovery</strong>, making the cumulative figure for recoveries standing at <strong>$totalRecovered</strong>.";
            $recoveredMs = "<strong>Tiada kes pemulihan</strong> yang dilaporkan, menjadikan jumlah kumulatif kes sembuh masih berada di <strong>$totalRecovered</strong> kes.";
            $recoveredZh = "今日<strong>无人康复</strong>出院，迄今累积康复人数保持在<strong>{$totalRecovered}</strong>人。";
        } else if (((int)$newRecovered) > 0) {
            $recoveredEn = "The ministry also reported that <strong>$newRecovered</strong> patients had recovered and discharged, bringing the cumulative figure for recoveries to <strong>$totalRecovered</strong> so far.";
            $recoveredMs = "<strong>$newRecovered</strong> kes sembuh dilaporkan semalam dan jumlah kumulatif kes sembuh adalah sebanyak <strong>$totalRecovered</strong> kes.";
            $recoveredZh = "我国昨日共有 <strong>$newRecovered</strong>人康复出院，迄今累积康复人数达 <strong>$totalRecovered</strong>人。";
        } else {
            $todayRecoveredEn = "";
            $todayRecoveredMs = "";
            $todayRecoveredZh = "";
        }

        if (((int)$newConfirmed) == 0) {
            $confirmedEn = "The Ministry of Health (MoH) announced that there were <strong>no new cases</strong> reported up till 12pm yesterday. The cumulative figure for positive cases in Malaysia remains at <strong>$totalConfirmed</strong>.";
            $confirmedMs = "Setakat jam 12 tengah hari, KKM mengumumkan bahawa <strong>tiada kes baru</strong> yang dicatatkan. Jumlah kumulatif kes positif di Malaysia kekal pada <strong>$totalConfirmed</strong> kes.";
            $confirmedZh = "截至今日中午12时，我国<strong>没有新增</strong>的确诊病例。目前累积确诊病例仍为<strong>$totalConfirmed</strong>宗。";
        } else if (((int)$newConfirmed) > 0) {
            $confirmedEn = "The Ministry of Health (MoH) announced that there were a total of <strong>$newConfirmed</strong> new cases reported up till 12pm yesterday, pushing the cumulative figure for positive cases in Malaysia to <strong>$totalConfirmed</strong>.";
            $confirmedMs = "Setakat jam 12 tengah hari semalam, Malaysia mencatatkan <strong>$newConfirmed</strong> kes baharu COVID-19, menjadikan jumlah kumulatif kes positif di Malaysia <strong>$totalConfirmed</strong> kes.";
            $confirmedZh = "截至昨日中午12时，我国新增 <strong>$newConfirmed</strong> 宗新冠肺炎确诊病例，目前累积确诊病例达 <strong>$totalConfirmed</strong>宗。";
        } else {
            $todayConfirmedEn = "";
            $todayConfirmedMs = "";
            $todayConfirmedZh = "";
        }

        // please note that html format below is important!! covid crawler is depending on it to modify some data
        $totalActiveHtml = "<span id='total-active-number'>" . $totalActive . "</span>";
        $totalConfirmedHtml = "<span id='total-confirmed-number'>" . $totalConfirmed . "</span>";
        $totalRecoveredHtml = "<span id='total-recovered-number'>" . $totalRecovered . "</span>";
        $totalDeathHtml = "<span id='total-death-number'>" . $totalDeath . "</span>";
        $totalBIDHtml = "<span id='total-bid-number'>" . $totalBID . "</span>";

        $newActiveSign = ($newActive != null && $newActive >= 0) ? '+' : '';
        $totalActiveStat = $totalActiveHtml . ' <span id="new-active-number">' . ($newActive != null ? '(' . $newActiveSign . $newActive . ')' : '') . '</span>';
        $totalConfirmedStat = $totalConfirmedHtml . ' <span id="new-confirmed-number">' . ($newConfirmed > -1 ? '(+' . $newConfirmed . ')' : '') . '</span>';
        $totalRecoveredStat = $totalRecoveredHtml . ' <span id="new-recovered-number">' . ($newRecovered > -1 ? '(+' . $newRecovered . ')' : '') . '</span>';
        $totalDeathStat =  $totalDeathHtml . ' <span id="new-death-number">' . ($newDeath > -1 ? '(+' . $newDeath . ')' : '') . '</span>';
        $totalBIDStat =  $totalBIDHtml . ' <span id="new-bid-number">' . ($newBID > -1 ? '(+' . $newBID . ')' : '') . '</span>';

        $html =
            <<<BQ
        <div id='header-image'>$headerImg</div>
        <p>KUALA LUMPUR, <strong>$today</strong> — <span id='confirmed-paragraph'>$confirmedEn</span></p>
        <!--AD-->
        <p id='recovered-paragraph'>$recoveredEn</p>
        <!--AD-->
        <p id='death-paragraph'>$deathsEn</p>
        <!--AD-->
        <p>$customEnInput</p>
        <!--AD-->
        <p>Data as of <strong id='data-time'>$dataAsOf</strong>:</p>
        <!--AD-->
        <p>
            <ul id="covid-number-paragraph">
                <li>Current recovery rate is at <strong>$recoveredRate%</strong></li>
                <li>Active Cases: <strong>$totalActiveStat</strong></li>
                <li>Total Confirmed: <strong>$totalConfirmedStat</strong></li>
                <li>Total Recovered: <strong>$totalRecoveredStat</strong></li>
                <li>Total Deaths: <strong>$totalDeathStat</strong></li>
                <li>Total Brought In Dead (BID): <strong>$totalBIDStat</strong></li>
            </ul>
        </p>
        <!--AD-->
        <p>&nbsp;</p>
        <div id='html-gallery'>$htmlGallery</div>
        <p>More to follow...</p>
        <!--AD-->
        BQ;

        $html = str_replace("<p></p>", "", $html);
        if (!$dryMode) {
            $toUpdateEn = array_merge($toUpdate, [
                'title' => $title,
                'description' => $description,
                'html' => $html,
                'media' => $mediaIds
            ]);

            DB::table('article')->where('uniqueID', 'covid19__en')->update($toUpdateEn);
        }



        //ZH
        $title = $dateToUse->format('d') . '日新冠肺炎数据';
        $today = $carbonToday->format('d') . '';
        $description = ((int)$newConfirmed) > -1 ?  $newConfirmed . ' 新确诊.' : "";
        $html = <<<BQ
        <div id='header-image'>$headerImg</div>
        <p>(吉隆坡，<strong>$today</strong>日讯）<span id='confirmed-paragraph'>$confirmedZh</span></p>
        <!--AD-->
        <p id='recovered-paragraph'>$recoveredZh</p>
        <!--AD-->
        <p id='death-paragraph'>$deathsZh</p>
        <!--AD-->
        <p>$customZhInput</p>
        <!--AD-->
        <p>截至 <strong id='data-time'>$dataAsOf</strong>:</p>
        <!--AD-->
        <p>
            <ul id="covid-number-paragraph">
                <li>康复率: <strong>$recoveredRate%</strong></li>
                <li>活跃确诊病例: <strong>$totalActiveStat</strong></li>
                <li>累计确诊: <strong>$totalConfirmedStat</strong></li>
                <li>累计康复: <strong>$totalRecoveredStat</strong></li>
                <li>累计死亡: <strong>$totalDeathStat</strong></li>
                <li>累计送院前死亡 (BID): <strong>$totalBIDStat</strong></li>
            </ul>
        </p>
        <!--AD-->
        <p>&nbsp;</p>
        <div id='html-gallery'>$htmlGallery</div>
        <p>更新中...</p>
        <!--AD-->
        BQ;
        $html = str_replace("<p></p>", "", $html);

        if (!$dryMode) {
            $toUpdateZh = array_merge($toUpdate, [
                'title' => $title,
                'description' => $description,
                'html' => $html,
                'media' => $mediaIds
            ]);

            DB::table('article')->where('uniqueID', 'covid19__zh')->update($toUpdateZh);
        }


        $title = $dateToUse->format('d F') . ' Kes COVID-19';
        $today = $carbonToday->format('d F');
        $description = ((int)$newConfirmed) > -1 ?  $newConfirmed . ' kes baru.' : "";
        $html = <<<BQ
        <div id='header-image'>$headerImg</div>
        <p>KUALA LUMPUR, <strong>$today</strong>: <span id='confirmed-paragraph'>$confirmedMs</span></p>
        <!--AD-->
        <p id='recovered-paragraph'>$recoveredMs</p>
        <!--AD-->
        <p id='death-paragraph'>$deathsMs</p>
        <!--AD-->
        <p>$customMsInput</p>
        <!--AD-->
        <p>Data setakat <strong id='data-time'>$dataAsOf</strong>:</p>
        <!--AD-->
        <p>
            <ul id="covid-number-paragraph">
                <li>Kadar kepulihan: <strong>$recoveredRate%</strong></li>
                <li>Kes Aktif: <strong>$totalActiveStat</strong></li>
                <li>Jumlah Kes Positif: <strong>$totalConfirmedStat</strong></li>
                <li>Jumlah Sembuh: <strong>$totalRecoveredStat</strong></li>
                <li>Jumlah Kematian: <strong>$totalDeathStat</strong></li>
                <li>Jumlah Brought In Dead (BID): <strong>$totalBIDStat</strong></li>
            </ul>
        </p>
        <!--AD-->
        <p>&nbsp;</p>
        <div id='html-gallery'>$htmlGallery</div>
        <p>Akan menyusul...</p>
        <!--AD-->
        BQ;
        $html = str_replace("<p></p>", "", $html);

        if (!$dryMode) {
            $toUpdateMs = array_merge($toUpdate, [
                'title' => $title,
                'description' => $description,
                'html' => $html,
                'media' => $mediaIds
            ]);

            DB::table('article')->where('uniqueID', 'covid19__ms')->update($toUpdateMs);
        }


        sleep(1);
        Cache::forget("a_covid19__en_2");
        Cache::forget("a_covid19__ms_2");
        Cache::forget("a_covid19__zh_2");
        try {
            $this->emitCovidEvent();
        } catch (\Exception $e){
            notify_now($e);
        }
        return  response($html);
    }

    private function emitCovidEvent($uniqueIds = ['covid19__en', 'covid19__ms', 'covid19__zh']): void{
        /** @var  ContentService $service */
        $service = app(ContentService::class);
        $articles = $service->getFeedContentObjectByUniqueIds($uniqueIds);
        foreach ($articles as $article){
            event(new CovidContentUpdatedEvent($article));
        }

    }
    public function createNewVideoFbPublisher(CreateVideoFbPublisherRequest $req, PublisherRepository $repo)
    {
        $workers = ["w1", "w2", "w3"];



        $name = $req->getName();
        $lang = $req->getLanguage();
        $imageUrl = $req->getImageLink();
        $description = $req->getDescription();
        $fbUrl = trim($req->getFbPageUrl(), "/");
        $existingPublisher = $req->getPublisherID()[0];
        $verified = $req->isVerified();

        if (!$lang) {
            abort(422, "invalid language");
        }
        if ($imageUrl) {
            $publisherIconUrl = $this->upload_object($imageUrl, $name);
        }

        $feedUrl = $this->getFetchRSSFeed($fbUrl);

        $publisherId = '';
        if (!isset($existingPublisher)) {
            $publisherId = DB::table('publishers')->insertGetId([
                "enabled" => 1,
                "name" => $name,
                'verified' => $verified ? 1 : 0,
                "description" => $description ?? $name,
                "logo_url" => $publisherIconUrl,
                "banner_url" => $publisherIconUrl,
                "language" => implode(',', $lang),
                "updated_at" => time(),
                "created_at" => time(),
            ]);
        } else {
            $publisherId = $existingPublisher;
        }

        $repo->addVideoChannel($fbUrl, $name, $lang, $publisherIconUrl, $publisherId);

        $publisher = DB::table('publishers')->where('id', $publisherId)->get();
        $channels = DB::table('channels')->where('publisher_id', $publisherId)->get();
        $publisher = $publisher->map(function ($item) use ($channels) {
            $channel = $channels->where('publisher_id', $item->id)->first();
            Carbon::resetToStringFormat();
            $item->type = "publisher";
            $item->isReaderMode = $channel->reader_view_only ?? 0;
            $item->channelIDEN = $channels->where('publisher_id', $item->id)->where('language', 'en')->first()->id ?? 0;
            $item->channelIDMS = $channels->where('publisher_id', $item->id)->where('language', 'ms')->first()->id ?? 0;
            $item->channelIDZH = $channels->where('publisher_id', $item->id)->where('language', 'zh')->first()->id ?? 0;
            return $item;
        })->toArray();

        $data = [
            "show_name" => $name,
            "name" => empty(Str::slug($name)) ? $name : Str::slug($name),
            "publisher_id" => $publisherId,
            "enabled" => true,
            "partner" => true,
            "need_random" => false,
            "language" => $lang,
            "frequency" => 15,
            "host" => $fbUrl,
            'endpoint' => $feedUrl,
            "type" => "fb-fetch-json",
            "worker" => $workers[array_rand($workers)],
            "crawl_type" => "video",
            "channelEN" => $publisher[0]->channelIDEN,
            "channelMS" => $publisher[0]->channelIDMS,
            "channelZH" => $publisher[0]->channelIDZH,
        ];
        notify_crawler_channel($data);

        return $publisher;
    }

    /**
     * @deprecated use createNewPublisherV2 instead
     * !deprecated use createNewPublisherV2 instead
     */
    public function createNewPublisher(CreatePublisherRequest $req, PublisherRepository $publisherRepo)
    {
        $workers = ["w1", "w2", "w3"];

        $type = "article";
        $name = $req->getName();
        $host = trim($req->getHost(), "/");
        $langs = $req->getLanguages();
        $readerMode = $req->isReaderMode();
        $imageUrl = $req->getImageLink();
        $description = $req->getDescription();
        $existingPublisher = $req->getPublisherID()[0];
        $verified = $req->isVerified();
        $gaid = $req->getGAId();
        $topic = $req->getTopic();
        $enabled = $req->isEnabled();
        $autoFollow = $req->isAutoFollow();

        $feed = $host;
        if (Str::contains($host, 'facebook.com') || Str::contains($host, 'youtube.com')) {
            $type = 'video';
        }
        if ($this->checkPublisherName($name)) {
            return response()->json(['message' => 'Publisher Name Already Exists!'], 400);
        }

        if ($imageUrl) {
            $publisherIconUrl = $this->upload_object($imageUrl, $name);
        }
        if (!count($langs)) abort(400, "invalid languages");

        $publisherId = '';
        if (!isset($existingPublisher)) {
            $publisherId = $publisherRepo->addPublisher($name, $description, $publisherIconUrl, $langs, $verified, $gaid, $enabled, 'https://lokalwav.newswav.com');
        } else {
            $publisherId = $existingPublisher;
        }
        try {
            DB::connection('admin_cluster')->table('publishers')->updateOrInsert(
                ['id' => $publisherId],
                ['name' => $name]
            );
        } catch (\Throwable $e) {
            notify_now($e);
        }

        if ($type == 'video') {
            $feed = $this->getFetchRSSFeed($host);
            $publisherRepo->addVideoChannel($host, $name, $langs, $publisherIconUrl, $publisherId);
        } else {
            $publisherRepo->addChannel($host, $name, $langs, $publisherIconUrl, $readerMode, $publisherId, $enabled);
        }

        if ($topic != null || $topic != "") {
            $publisherRepo->addPublisherToTopic($publisherId, $topic);
        } else {
            //for bq fix. yes, i know. very messy :(
            $req->merge(['topic' => 0]);
        }
        if ($autoFollow != 0) {
            $publisherRepo->appendToDefaultFollowList([], [$publisherId]);
        } else {
            $publisherRepo->popFromDefaultFollowList(null, $publisherId);
        }
        $publisher = DB::table('publishers')->where('id', $publisherId)->get();
        $channels = DB::table('channels')->where('publisher_id', $publisherId)->get();
        $topics = DB::table('segmentTopicParent')->get();
        $default_follow = DB::table('default_follow_list')->whereNull('deleted_at')->get();
        $publisher = $publisher->map(function ($item) use ($channels, $topics, $default_follow) {
            $topic = '-';
            foreach ($topics as $parent) {
                $newSegmentPublisherArray = explode(',', $parent->segmentPublisherArray);
                $newSegmentPublisherArrayForAndroid = explode(',', $parent->segmentPublisherArrayForAndroid);

                if (in_array($item->id, $newSegmentPublisherArray)) {
                    $topic = $parent->id;
                    break;
                } elseif (in_array($item->id, $newSegmentPublisherArrayForAndroid)) {
                    $topic = $parent->id;
                    break;
                }
            }
            $content['article'] = $channels->where('publisher_id', $item->id)->where('has_articles', 1)->first() ?? false;
            $content['videos'] = $channels->where('publisher_id', $item->id)->where('has_videos', 1)->first() ?? false;
            $content['podcast'] = $channels->where('publisher_id', $item->id)->where('has_podcasts', 1)->first() ?? false;
            $set = [];
            foreach ($content as $key => $val) {
                if ($key == 'article' && $val) {
                    array_push($set, 'Articles');
                } elseif ($key == 'videos' && $val) {
                    array_push($set, 'Videos');
                } elseif ($key == 'podcast' && $val) {
                    array_push($set, 'Podcasts');
                }
            }
            $item->autoFollow = $default_follow->where('feed_id', 'F_P_' . $item->id)->first() ? 1 : 0;
            $item->content = implode(',', $set);
            $channel = $channels->where('publisher_id', $item->id)->first();
            Carbon::resetToStringFormat();
            $item->topic = $topic;
            $item->type = "publisher";
            $item->isReaderMode = $channel->reader_view_only ?? 0;
            $item->channelIDEN = $channels->where('publisher_id', $item->id)->where('language', 'en')->first()->id ?? 0;
            $item->channelIDMS = $channels->where('publisher_id', $item->id)->where('language', 'ms')->first()->id ?? 0;
            $item->channelIDZH = $channels->where('publisher_id', $item->id)->where('language', 'zh')->first()->id ?? 0;
            return $item;
        })->toArray();

        $data = [
            "show_name" => $publisher[0]->name,
            "name" => empty(Str::slug($name)) ? $name : Str::slug($name),
            "host" => $host,
            "endpoint" => $feed,
            "publisher_id" => $publisher[0]->id,
            "enabled" => true,
            "partner" => true,
            "need_random" => false,
            "ga_id" => $publisher[0]->ga_id,
            "frequency" => 15,
            "channelEN" => $publisher[0]->channelIDEN,
            "channelMS" => $publisher[0]->channelIDMS,
            "channelZH" => $publisher[0]->channelIDZH,
            "worker" => $workers[array_rand($workers)],
        ];
        if ($type == 'video') {
            $data["type"] = "fb-fetch-json";
            $data["crawl_type"] = $type;
        }
        notify_crawler_channel($data);

        return $publisher;
    }

    /**
     * It creates a new publisher
     *
     * @param \App\Http\Requests\Admin\CreatePublisherRequest req the request object
     * @param \App\Repositories\Publisher\PublisherRepository repo publisher repository
     *
     * @return The publisher object
     */
    public function createNewPublisherV2(CreatePublisherRequest $req, PublisherRepository $repo)
    {
        $name = $req->getName();
        $langs = $req->getLanguages();
        $imageUrl = $req->getImageLink();
        $description = $req->getDescription();
        $verified = $req->isVerified();
        $gaid = $req->getGAId();
        $topics = $req->getTopics();
        $enabled = $req->isEnabled();
        $autoFollow = $req->isAutoFollow();
        $project = $req->getProjectType();
        $ad_share = $req->getAdShare();
        $website_url = $req->getWebsiteUrl();

        if ($this->checkPublisherName($name)) {
            return response()->json(['message' => 'Publisher Name Already Exists!'], 400);
        }

        if ($imageUrl) {
            $publisherIconUrl = $this->upload_object($imageUrl, $name);
        }

        //NWGEN-490
        // if (!count($langs)) abort(400, "invalid languages");

        DB::beginTransaction();

        try {
            $publisherId = $repo->addPublisher($name, $description, $publisherIconUrl, $langs, $verified, $gaid, $enabled, $project, $ad_share, $website_url);

            if (!$publisherId){
                DB::rollback();
                Log::warning("No published id after creation, rollback and abort");
                abort(400, "No published id after creation, rollback and abort");
            }

            if ($topics != null || $topics != []) {
                $repo->addPublisherToTopic($publisherId, $topics);
                //for bq auth log, current datatype is string
                $req->merge(['topic' => implode(',', $topics)]);
            } else {
                //for bq fix. yes, i know. very messy :(
                $req->merge(['topic' => 0]);
            }
            if ($autoFollow != 0) {
                $repo->appendToDefaultFollowList([], [$publisherId]);
            } else {
                $repo->popFromDefaultFollowList(null, $publisherId);
            }



            $publisher = DB::table('publishers')->where('id', $publisherId)->get();

            if (count($publisher) == 0){
                DB::rollback();
                Log::warning("Publisher not found based on created publisher id");
                abort(400, "Publisher not found based on created publisher id");
            }

            $default_follow = DB::table('default_follow_list')->whereNull('deleted_at')->get();

            DB::commit();

            return $publisher->map(function ($item) use ($topics, $default_follow) {
                $item->autoFollow = $default_follow->where('feed_id', 'F_P_' . $item->id)->first() ? 1 : 0;
                $item->topics = count($topics) > 0 ? $topics : ['-'];
                $item->type = "publisher";
                return $item;
            })->toArray();
        } catch (\Throwable $e){
            DB::rollback();
            notify_now($e);
            abort(400, $e->getMessage());
        }

    }

    public function createPublisherChannel(CreatePublisherRequest $req, PublisherRepository $repo, $pub_id)
    {
        $chType = $req->getChannelType();
        $langs = $req->getChannelLanguage();
        $host = trim($req->getHost(), "/");
        $readerMode = $req->isReaderMode();
        $enabled = $req->isEnabled();
        $feed = false;
        if ($repo->existingChannel($langs, $chType, $pub_id)) {
            return response()->json(['msg' => 'Content Type + Language Already Exist!'], 400);
        }
        if ($chType == 'video') {
            $hosts = explode(',', $host)[0];
            if (Str::contains($hosts, 'facebook.com')) {
                $feed = $this->getFetchRSSFeed($hosts);
            }
        }
        return $repo->addChannelV2($host, $langs, $readerMode, $pub_id, $chType, $feed, $enabled);
    }

    public function editPublisherChannel(CreatePublisherRequest $req, PublisherRepository $repo, $ch_id)
    {
        $chType = $req->getChannelType();
        $langs = $req->getChannelLanguage();
        $host = trim($req->getHost(), "/");
        $readerMode = $req->isReaderMode();
        $enabled = $req->isEnabled();
        $crawlerFrequency = $req->getCrawlerFrequency();
        $pub_id = $req->input('publisher_id');
        if ($repo->existingChannel($langs, $chType, $pub_id, $ch_id)) {
            return response()->json(['msg' => 'Content Type + Language Already Exist!'], 400);
        }

        $data = $repo->editChannel($ch_id, $readerMode, $host, $langs, $chType, $enabled, $crawlerFrequency);
        $repo->updatePublisherLanguages($pub_id);
        return response()->json($data);
    }

    public function editPublisherV2(PublisherRepository $repo, CreatePublisherRequest $req, $id)
    {
        $description = $req->getDescription();
        $name = $req->getName();
        $imageLink = $req->getImageLink();
        $verified = $req->isVerified();
        $enabled = $req->isEnabled();
        $langs = $req->getLanguages($id);
        $gaid = $req->getGAId();
        $topics = $req->getTopics();
        $autoFollow = $req->isAutoFollow();
        $project = $req->getProjectType();
        $readerMode = $req->isReaderMode();
        $ad_share = $req->getAdShare();
        $website_url = $req->getWebsiteUrl();

        if ($this->checkPublisherName($name, $id)) {
            return response()->json(['message' => 'Publisher Name Already Exists!'], 400);
        }
        $topicParent = DB::table('segmentTopicParent')->get();
        $found = [];
        foreach ($topicParent as $parent) {
            $newSegmentPublisherArray = explode(',', $parent->segmentPublisherArray);
            $newSegmentPublisherArrayForAndroid = explode(',', $parent->segmentPublisherArrayForAndroid);
            if (in_array($id, $newSegmentPublisherArray)) {
                $found[] = $parent->id;
            } elseif (in_array($id, $newSegmentPublisherArrayForAndroid)) {
                $found[] = $parent->id;
            }
        }
        //remove every existing topic found
        if (count($found) > 0){
            $repo->removePublisherFromTopic($id, array_unique($found));
        }
        //reinsert new topics from request if enabled = 1
        if (count($topics) && $enabled == 1) {
            $repo->addPublisherToTopic($id, $topics);
        }

        if ($autoFollow != 0) {
            $repo->appendToDefaultFollowList([], [$id]);
        } else {
            $repo->popFromDefaultFollowList(null, $id);
        }
        //different branch have changes on here, making separate checking to avoid conflict
        event(new AdminUpdatePublisherEvent($id, $this->getAdminUser()));
        return $repo->editPublisherV2($id, $description, $langs, $readerMode, $imageLink, $name, $verified, $enabled, $gaid, $project, $ad_share, $website_url);
    }

    public function listPublishers(PublisherRepository $repo, ListPublisherRequest $req)
    {
        $limit = $req->getLimit();
        $id = $req->getId();
        $name = $req->getName();
        $contentType = $req->getContentType();
        $verified = $req->getBoolean('verified');
        $status = $req->getBoolean('status');
        $native = $req->getBoolean('native');
        $native = $req->getNative();
        $languages = $req->getLanguages();
        $gaTrackingId = $req->getGaId();
        $type = $req->getProjectType();
        $autoFollow = $req->getBoolean('autoFollow');
        $adShareRevenue = $req->getAdShare();
        $topics = $req->getTopics();
        $sort = $req->getSort();
        return response()->json($repo->listAllPublishers(
                $id,
                $limit,
                $name,
                $contentType,
                $verified,
                $status,
                $native,
                $languages,
                $gaTrackingId,
                $type,
                $autoFollow,
                $adShareRevenue,
                $topics,
                $sort
            )
        );

    }

    public function getPublisherById($publisherId, PublisherRepository $repo){
        $publisher = $repo->getPublisherById($publisherId);

        if (!$publisher){
            return response()->json(['message' => 'publisher not found'], 400);
        }
        return response()->json($publisher);
    }

    public function listTopics()
    {
        $topics = DB::table('segmentTopicParent')->get();
        $data = [];
        foreach ($topics as $item) {
            $data[] = [
                'value' => $item->id,
                'label' => $item->nameEN
            ];
        }
        return response()->json($data);
    }

    /**
     * @deprecated please use editPublisherV2 instead
     * !deprecated please use editPublisherV2 instead
     */
    public function editPublisher(PublisherRepository $repo, CreatePublisherRequest $req, $id)
    {
        $description = $req->getDescription();
        $name = $req->getName();
        $imageLink = $req->getImageLink();
        $readerMode = $req->isReaderMode();
        $verified = $req->isVerified();
        $enabled = $req->isEnabled();
        $langs = $req->getLanguages();
        $gaid = $req->getGAId();
        $topic = $req->getTopic();
        $autoFollow = $req->isAutoFollow();
        $project = $req->getProjectType();

        if ($this->checkPublisherName($name, $id)) {
            return response()->json(['message' => 'Publisher Name Already Exists!'], 400);
        }
        $topicParent = DB::table('segmentTopicParent')->get();
        foreach ($topicParent as $parent) {
            $found = '';
            $newSegmentPublisherArray = explode(',', $parent->segmentPublisherArray);
            $newSegmentPublisherArrayForAndroid = explode(',', $parent->segmentPublisherArrayForAndroid);
            if (in_array($id, $newSegmentPublisherArray)) {
                $found = $parent->id;
            } elseif (in_array($id, $newSegmentPublisherArrayForAndroid)) {
                $found = $parent->id;
            }
            if ($found !== $topic) {
                $repo->removePublisherFromTopic($id, $found);
            }
        }
        if ($topic != "") {
            $repo->addPublisherToTopic($id, $topic);
        }
        if ($enabled == 0) {
            $repo->removePublisherFromTopic($id, $topic);
        }
        if ($autoFollow != 0) {
            $repo->appendToDefaultFollowList([], [$id]);
        } else {
            $repo->popFromDefaultFollowList(null, $id);
        }
        $publisher = $repo->editPublisher($id, $description, $langs, $imageLink, $name, $readerMode, $verified, $enabled, $gaid, $project);

        if ($topic == "-") {
            //for bq fix. yes, i know. very messy :(
            $req->merge(['topic' => 0]);
        }

        return response()->json($publisher);
    }

    private function checkPublisherName($name, $id = false)
    {
        $data = DB::table('publishers')
            ->when($id, function ($query) use ($id) {
                return $query->whereNotIn('id', [$id]);
            })
            ->when($name, function ($query) use ($name) {
                $names = explode(' ', $name);
                foreach ($names as $item) {
                    $item = trim($item);
                    $query->where('name', $item);
                }
                return $query;
            })
            ->get();
        return count($data) > 0;
    }

    public function listScheduledPN(ScheduledPnService $service, $id = false)
    {
        if ($id) {
            return response()->json($service->scheduledPNs($id));
        }
        return response()->json($service->scheduledPNs());
    }

    public function editScheduledPN(SendPn4Request $req, ScheduledPnService $service, $id)
    {
        $adminId = $req->getAdminId();
        $adminName = $req->getAdminName();
        $payload = $req->input('payload');
        $sendTime = Carbon::parse($req->getScheduledPnTime())->timestamp;
        $status = $req->input('status') ?? "scheduled";
        if ($status != 'cancelled') {
            if (Carbon::parse($req->getScheduledPnTime())->isPast()) {
                abort(400, "Scheduled PN should be in future");
            }
            // if (!Carbon::parse($req->getScheduledPnTime())->subHours(12)->isPast()) {
            //     abort(400, "Scheduled PN should be after 12 hours at most");
            // }
        }
        return $service->updateScheduledPn($adminId, $payload, $sendTime, $adminName, $status, $id);
    }

    public function totalList(PublisherRepository $repo)
    {

        return response()->json(['total_list' => $repo->getTotalList()]);
    }

    public function listDefaultFollowList(PublisherRepository $repo)
    {
        return response()->json(['follow_list' => $repo->getDefaultFollowList()]);
    }

    public function addToDefaultFollowList(DefaultFollowListRequest $req, PublisherRepository $repo)
    {
        $topics = $req->getTopics();
        try {
            $repo->appendToDefaultFollowList($topics, []);
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['msg' => 'Something went wrong in adding selection(s) to default follow list'], 400);
        }
        return response()->json(['msg' => 'Successfully added selection(s) to default follow list']);
    }

    public function removeFromDefaultFollowList(PublisherRepository $repo, $id)
    {
        try {
            $repo->popFromDefaultFollowList($id, null);
        } catch (\Exception $e) {
            return response()->json(['msg' => 'Something went wrong in removing from default follow list'], 400);
        }
        return response()->json(['msg' => 'Successfully removed from default follow list']);
    }

    function downloadMalaysiaData($lang = 'en')
    {

        $columns = array('Date', 'Daily Confirmed', 'Daily Cured');
        switch ($lang) {
            case 'ms':
                $columns = array('Tarikh', 'Kes Harian', 'Kes Sembuh Harian');
                break;
            case 'zh':
                $columns = array('日期', '单日确诊', '单日康复');
                break;
        }

        $stats = DB::table('corona_stats_2')
            ->select(DB::raw('DATE_FORMAT(stats_date,"%d %b %y") as stats_date, stats_date as date, sum(confirmed) as confirmed, sum(cured) as cured'))
            ->where('country', 'Malaysia')
            ->where('confirmed', ">", 'o')
            ->where('stats_date', ">", Carbon::now()->subDays(91)->format("y-m-d"))
            ->orderByDesc('date')
            ->groupBy('stats_date')
            ->get();

        //return response()->json($stats);
        $dates = $stats->pluck('date')->toArray();
        $latestdate = $stats->max('date');
        $oldestdate = $stats->min('date');
        $new = collect();
        $stats->values();

        foreach ($dates as $key => $date) {
            if ($date != $oldestdate) {
                $stats_date = $stats->get($key)->stats_date;
                $con = $stats->get($key)->confirmed - $stats->get($key + 1)->confirmed;
                $cured = $stats->get($key)->cured - $stats->get($key + 1)->cured;
                if ($date == $latestdate) {
                    if ($con == 0) {
                        $con = null;
                    }
                    if ($cured == 0) {
                        $cured = null;
                    }
                }
                $a = (object)['stats_date' => $stats_date, 'confirmed' => $con, 'cured' => $cured, 'date' => $date];

                $new->push($a);
            }
        }
        $new = $new->sortBy('date');
        $file = storage_path() . "/app/malaysia-$lang-" . time() . ".csv";
        $f = fopen($file, "w");
        fputcsv($f, $columns);
        foreach ($new as $stat) {
            fputcsv($f, array($stat->stats_date, $stat->confirmed, $stat->cured));
        }
        fclose($f);
        return response()->download($file);
    }

    function downloadGlobalData($lang = 'en')
    {
        $columns = array('Date', 'Total Confirmed', 'Total Cured');
        switch ($lang) {
            case 'ms':
                $columns = array('Tarikh', 'Confirmed', 'Sembuh');
                break;
            case 'zh':
                $columns = array('日期', '确诊', '康复');
                break;
        }
        $stats = DB::table('corona_stats_2')
            ->select(DB::raw('DATE_FORMAT(stats_date,"%d %b %y") as stats_date,sum(confirmed) as confirmed, sum(cured) as cured'))
            ->where('stats_date', ">", Carbon::now()->subDays(90)->format("y-m-d"))
            ->groupBy('stats_date')
            ->get();
        $file = storage_path() . "/app/global-$lang-" . time() . ".csv";
        $f = fopen($file, "w");
        fputcsv($f, $columns);
        foreach ($stats as $stat) {
            fputcsv($f, array($stat->stats_date, $stat->confirmed, $stat->cured));
        }
        fclose($f);
        return response()->download($file);
    }

    function clearRedisArticles(Request $request): void
    {
        $articles = explode(',', $request->input('articles'));
        foreach ($articles as $article) {
            Cache::forget("a_" . $article . "_2");
        }
    }



    function clearRedisArticles2(Request $request): void
    {
        $articles = explode(',', $request->input('articles'));
        foreach ($articles as $article) {
            Cache::forget("a_" . $article . "_2");
        }
    }

    function enablePublishersAndChannels(EnablePublisherAndChannelsRequest $request,  PublisherRepository $publisherRepo): void
    {
        $value = $request->getValue() === 0 ? 0 : 1;
        $channels = $request->getChannelsId() ? explode(",", $request->getChannelsId() ?? "") : [];
        $publishers = $request->getPublishersId() ? explode(",", $request->getPublishersId() ?? "") : [];
        $publisherRepo->togglePublisherAndChannel($value, $publishers, $channels);
    }

    private function upload_object($source_path, $publisherName = null)
    {
        $cdnUrl = nw_bunker('gcloud', 'cdn_newswav_url', 'https://cdn.newswav.com/');
        $bucketName = nw_bunker('gcloud', 'storage_cdn_bucket', 'cdn.newswav.com');

        $folder =  $publisherName ? 'nonredirect/publisherImage/'  : "pn_images/";
        $name = ($publisherName ? $publisherName  : "pn_image") . '_' . time() . '_' . rand(0, 9999) . '.jpg';
        $oldPublisherName = $publisherName;
        $publisherName = Str::slug($publisherName, '-');
        if (empty($publisherName)) $publisherName = $oldPublisherName;
        $new_path = $folder . $name;

        $storage = new StorageClient([
            'projectId' => config('cloud.project_id'),
            'keyFilePath' => config('cloud.storage.key_path')
        ]);
        $bucket = $storage->bucket($bucketName);
        $object = $bucket->upload(
            file_get_contents($source_path),
            [
                'name' => $new_path,
                'predefinedAcl' => 'publicRead',
                'metadata' => [
                    'metadata' => [
                        'Cache-Control' => 'public, max-age=18000'
                    ]
                ]
            ]
        );
        return $cdnUrl . $new_path;
    }

    public function toggleChannelView(ChannelsViewModeRequest $req, PublisherRepository $publisherRepo)
    {
        $channelIDs = $req->getChannelIds() ? explode(',', $req->getChannelIds() ?? "") : [];
        $reader = $req->isReaderMode() === 0 ? 0 : 1;
        $publisherRepo->toggleChannelView($channelIDs, $reader);
        return response()->json(["Message" => "Channels have been updated", "Channel IDs" => $channelIDs]);
    }

    public function addPublisherToExplore(AddPublisherToExploreRequest $req)
    {
        $parentTopicIDs = $req->getParentTopicIds() ? explode(',', $req->getParentTopicIds()) : [];
        $publisherIDs = $req->getPublisherIds() ? explode(',', $req->getPublisherIds()) : [];
        $topicIDs = $req->getTopicIds() ? explode(',', $req->getTopicIds()) : [];
        $toRemove = $req->isRemove();
        if (!$toRemove) {
            if (count($publisherIDs)) {
                $topicParent = DB::table('segmentTopicParent')
                    ->whereIn('id', $parentTopicIDs)
                    ->get();

                foreach ($topicParent as $parent) {

                    $newSegmentTopicArray = array($parent->segmentTopicArray);
                    $newSegmentPublisherArray = array($parent->segmentPublisherArray);
                    $newSegmentPublisherArrayForAndroid = array($parent->segmentPublisherArrayForAndroid);

                    foreach ($topicIDs as $top) {
                        if (!in_array($top, $newSegmentTopicArray)) {
                            array_push($newSegmentTopicArray, $top);
                        }
                    }

                    foreach ($publisherIDs as $pub) {
                        if (!in_array($pub, $newSegmentPublisherArray)) {
                            array_push($newSegmentPublisherArray, $pub);
                        }

                        if (!in_array($pub, $newSegmentPublisherArrayForAndroid)) {
                            array_push($newSegmentPublisherArrayForAndroid, $pub);
                        }
                    }


                    DB::table('segmentTopicParent')
                        ->where('id', $parent->id)
                        ->update([
                            'segmentTopicArray' => implode(',', $newSegmentTopicArray),
                            'segmentPublisherArray' => implode(',', $newSegmentPublisherArray),
                            'segmentPublisherArrayForAndroid' => implode(',', $newSegmentPublisherArrayForAndroid)
                        ]);
                }
            }
            $msg = "Publishers are added to selected topic";
        } else {
            $this->removePublisherFromExplore($publisherIDs, $topicIDs, $parentTopicIDs);
            $msg = "Publishers are removed from selected parent topic";
        }
        return response()->json(["Message" => $msg, "Parent Topic IDs" => $parentTopicIDs, "Publisher IDs" => $publisherIDs, "Topic IDs" => $topicIDs]);
    }

    public function listSabahArticles(AdminRequest $req)
    {
        $articles = DB::table('segment_feed_articles')
            ->where('feed_id', 8)
            ->where('content_type', 'a')
            ->orderByDesc('published_date')
            ->limit(100)
            ->get();
        $articleIds = $articles->pluck('article_unique_id')->toArray();
        $links = $this->getArticleLinks($articleIds);
        $articles->map(function ($item) use ($links) {
            $item->url = $links->get($item->article_unique_id)->canonicalURL;
        });
        return response()->json($articles);
    }

    public function listSabahNewsSocialFeed(AdminRequest $req)
    {
        $base = Carbon::now()->setTime(0, 0, 0, 0);
        $today = Carbon::now()->setTime(0, 0, 0, 0);
        $end = $base->subDays(1);
        $news = DB::table('feeds')
            ->where('publishedDate', '>=', $end)
            ->where('publishedDate', '<=', $today)
            ->orderByDesc('publishedDate')
            ->get();
        return response()->json($news);
    }

    private function getArticleLinks($ids)
    {
        return DB::table('article')->select('canonicalURL', 'uniqueID')->whereIn('uniqueID', $ids)->get()->keyBy('uniqueID');
    }

    public function toggleArticleInFeed(ToggleArticleFeedRequest $req)
    {

        $articleIds = explode(',', $req->getArticleIds());
        $feedId = $req->getFeedId();
        $enabled = $req->isEnable();
        if (count($articleIds)) {
            DB::table('segment_feed_articles')
                ->where('feed_id', $feedId)
                ->whereIn('article_unique_id', $articleIds)
                ->update(['enabled' => $enabled]);
        } else {
            abort(422, "Missing article ids");
        }
        $article = DB::table('segment_feed_articles')->whereIn('article_unique_id', $articleIds)->get();
        return response()->json($article);
    }



    public function sendFcmPn(SendFcmRequest $req)
    {
        $platform =   $req->getPlatform();
        $articleUniqueID =   $req->getArticleUniqueIdPlatform();
        $customTitle = $req->getCustomTitle();
        $customMessage = $req->getCustomMessage();
        $language = $req->getLanguage();
        $result = $this->sendFcmPnNow(
            $platform,
            $articleUniqueID,
            $customTitle,
            $customMessage,
            $language
        );
        return response()->json($result);
    }


    private function sendFcmPnNow(
        $platform,
        $articleUniqueID,
        $customTitle,
        $customMessage,
        $language
    ) {
        $article = DB::table('article')->where('uniqueID', $articleUniqueID)->first();
        if (!$article) abort(404, "Article not found");
        $title = $article->title;
        $description = strip_tags($article->html);
        $description = trim($description);
        $descriptionLength = mb_strlen($description);
        $fitLength = 130;
        if ($language == 'zh') {
            $fitLength = 46;
        }
        $maxLength = min($descriptionLength, $fitLength);
        $body = mb_substr($description, 0, $maxLength);
        if ($descriptionLength > $fitLength) {
            $body = $body . '...';
        }

        $prefix = ($platform == "android") ? "/topics/and-PNPeriod" : "/topics/PNPeriod";
        $prefixV2 = ($platform == "android") ? "/topics/and-PNPeriodV2" : "/topics/PNPeriodV2";
        $topicArray = array("$prefix-3-$language", "$prefix-1-$language", "$prefix-2-$language");
        $topicArrayV2 = array("$prefixV2-3-$language", "$prefixV2-1-$language", "$prefixV2-2-$language");

        $prefix = ($platform == "android") ? "and-PNPeriod" : "PNPeriod";
        $prefixV2 = ($platform == "android") ? "and-PNPeriodV2" : "PNPeriodV2";
        $aCondition = "'$prefix-1-$language' in topics || '$prefix-2-$language' in topics || '$prefix-3-$language' in topics";
        $aConditionV2 = "'$prefixV2-1-$language' in topics || '$prefixV2-2-$language' in topics || '$prefixV2-3-$language' in topics";


        $type = '0';

        $mediaArrayString = $article->media;
        $mediaArray = explode(",", $mediaArrayString);
        $mediaID = $mediaArray[0];
        /** @var MediaRepository $mediaRepository */
        $mediaRepository = app(MediaRepository::class);
        $media = $mediaRepository->findById($mediaID);
        $mediaURL = $media->url ?? null;
        $mediaLocalURL = '';
        if ($mediaURL) {
            try {
                $mediaLocalURL = $this->upload_object($mediaURL);
            } catch (\Exception $e) {
                $mediaLocalURL = $mediaURL;
            }
        }
        $topicArrayString = implode(",", array_filter($topicArray));

        $insertTitle = addslashes($title);
        $insertBody = addslashes($body);

        if ($customTitle != '') {
            $title = $customTitle;
        }
        if ($customMessage != '') {
            $body = $customMessage;
        }

        $adddate = date('Y-m-d H:i:s');
        $tableName = ($platform == "android") ? "android_push" : "push";
        $pushRowID = DB::connection('pn_cluster')
            ->table($tableName)->insertGetId([
                'type' => $type,
                'createdDate' => $adddate,
                'updatedDate' => $adddate,
                'articleUniqueID' => $articleUniqueID,
                'title' => $insertTitle,
                'message' => $insertBody,
                'FCMTopicArray' => $topicArrayString,
                'customTitle' => $customTitle,
                'customMessage' => $customMessage,
            ]);




        $apiKey = config('app.fcm_token');
        $FCMPushNotification = new FCMPushNotification($apiKey);
        $aPayload = false;

        if ($platform == 'android') {
            $aPayload = array(
                'data' => array(
                    "uniqueID" => $articleUniqueID,
                    "languageFlag" => $language,
                    "PNPeriod" => "1,2,3",
                    "pushRowID" => $pushRowID,
                    "type" => $type,
                    "media-type" => "image",
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    "media-url" => $mediaLocalURL
                )
            );
        } else {
            $aPayload = array(
                'data' => array("uniqueID" => $articleUniqueID, "pushRowID" => $pushRowID, "type" => $type, "media-url" => $mediaLocalURL, "media-type" => "image"),
                'notification' => array(
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    'mutable_content' => true,
                    'click_action' => 'Article'
                )
            );
        }

        $aOptions = array(
            'time_to_live' => 172800,
            'priority' => 'high'
        );

        $aResult = $FCMPushNotification->sendToCondition(
            $aCondition,
            $aPayload,
            $aOptions // optional
        );

        $version = '2';

        $topicArrayString = implode(",", array_filter($topicArrayV2));

        $pushRowID = DB::connection('pn_cluster')
            ->table($tableName)->insertGetId([
                'type' => $type,
                'version' => $version,
                'createdDate' => $adddate,
                'updatedDate' => $adddate,
                'articleUniqueID' => $articleUniqueID,
                'title' => $insertTitle,
                'message' => $insertBody,
                'FCMTopicArray' => $topicArrayString,
                'customTitle' => $customTitle,
                'customMessage' => $customMessage,
            ]);

        if ($platform == 'android') {
            $aPayload = array(
                'data' => array(
                    "uniqueID" => $articleUniqueID,
                    "languageFlag" => $language,
                    "PNPeriod" => "1,2,3",
                    "pushRowID" => $pushRowID,
                    "type" => $type,
                    "media-type" => "image",
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    "media-url" => $mediaLocalURL
                )
            );
        } else {
            $aPayload = array(
                'data' => array("uniqueID" => $articleUniqueID, "pushRowID" => $pushRowID, "type" => $type, "media-url" => $mediaLocalURL, "media-type" => "image"),
                'notification' => array(
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    'mutable_content' => true,
                    'click_action' => 'Article'
                )
            );
        }

        $aResult1 = $FCMPushNotification->sendToCondition(
            $aConditionV2,
            $aPayload,
            $aOptions // optional
        );

        // $aResult1 = [];
        // $aResult = [];

        return json_encode($aResult ?? "*") . " - " . json_encode($aResult1 ?? "*");
    }



    public function sendFcmPnAllPlatforms(SendFcmRequest2 $req)
    {
        $articleUniqueID =   $req->getArticleUniqueIdPlatform();
        $customTitle = $req->getCustomTitle();
        $customMessage = $req->getCustomMessage();
        $language = $req->getLanguage();
        //$results = [];
        //return response()->json($results);
        $results[] = $this->sendFcmPnNow(
            "android",
            $articleUniqueID,
            $customTitle,
            $customMessage,
            $language
        );
        $results[] = $this->sendFcmPnNow(
            "ios",
            $articleUniqueID,
            $customTitle,
            $customMessage,
            $language
        );
        return response()->json($results);
    }

    private function removePublisherFromExplore($publisherID, $topicID, $parentTopicID): void
    {
        if (count($parentTopicID) && count($publisherID)) {
            $topicParent = DB::table('segmentTopicParent')->whereIn('id', $parentTopicID)->get();

            foreach ($topicParent as $parent) {
                $tempTopic = explode(',', $parent->segmentTopicArray);
                $tempPublisher = explode(',', $parent->segmentPublisherArray);

                $newTopic = $newPublisher = $newAndroid = [];
                foreach ($topicID as $top) {
                    foreach ($tempTopic as $tempTop) {
                        if ($top !== $tempTop) {
                            $newTopic[] = $tempTop;
                        }
                    }
                }

                foreach ($publisherID as $pub) {
                    foreach ($tempPublisher as $tempPub) {
                        if ($pub !== $tempPub) {
                            $newPublisher[] = $tempPub;
                        }
                    }
                }

                DB::table('segmentTopicParent')
                    ->where('id', $parent->id)
                    ->update([
                        'segmentTopicArray' => implode(',', $newTopic),
                        'segmentPublisherArray' => implode(',', $newPublisher),
                    ]);
            }
        }
    }


    function saveToDb($rows, $tableName = "corona_stats_2"): void
    {
        $articlesString = "";
        foreach ($rows as $row) {
            $date = Carbon::createFromTimestamp($row['date'], 'UTC')->format('Y-m-d');
            $city = $row['city'] ?? "x";
            $suspected = $row['suspected'] ?? 0;
            $key = "$date,{$row['country']},{$city}";
            if (!$row['city']) {
                $articlesString  .=
                    "('$date','{$row['country']}', NULL, {$row['confirmed']}, {$suspected}, {$row['deaths']}, {$row['cured']}, {$row['date']}, '$key'),";
            } else {
                $articlesString  .=
                    "('$date','{$row['country']}','{$row['city']}', {$row['confirmed']},  {$suspected}, {$row['deaths']}, {$row['cured']}, {$row['date']}, '$key'),";
            }
        }

        $articlesString = trim($articlesString, ",");
        $q = "INSERT IGNORE INTO $tableName
            (stats_date, country, city, confirmed,suspected,death,cured, updated_from_source, unique_key)
            values $articlesString
            ON DUPLICATE KEY UPDATE
            confirmed = values(confirmed),
            death = values(death),
            cured = values(cured),
            suspected = values(suspected),
            updated_from_source = values(updated_from_source)
            ";
        DB::statement($q);
    }


    public function createPNChannel(CreatePNChannelRequest $req)
    {
        $name = $req->getName();
        $description = $req->getDescription();
        $image = $req->getImage();
        $byDefault = $req->getSubscribedByDefault() ?? 0;

        DB::connection('pn_cluster')->table('pn_channels')->insert([
            'name' => $name,
            'description' => $description,
            'header_image' => $image,
            'subscribe_by_default' => $byDefault
        ]);

        return [
            'message' => 'PN Channel created',
        ];
    }

    public function listPNChannel(AdminRequest $req)
    {
        $data = DB::connection('pn_cluster')->table('pn_channels')->get();
        return $data;
    }

    public function updatePNChannel(CreatePNChannelRequest $req, $pn_id)
    {
        $name = $req->getName();
        $description = $req->getDescription();
        $image = $req->getImage();
        $byDefault = $req->getSubscribedByDefault() ?? 0;

        DB::connection('pn_cluster')->table('pn_channels')
            ->where('id', $pn_id)
            ->update([
                'name' => $name,
                'description' => $description,
                'header_image' => $image,
                'subscribe_by_default' => $byDefault
            ]);

        return [
            'message' => 'PN Channel updated'
        ];
    }

    public function createFeed(AdminRequest $req)
    {
        $id = DB::table('app_feed')->insertGetId([
            'feed_id' => $req->input('feed_id'),
            'feed_type' => $req->input('feed_type') ?? null,
            'comments_enabled' => $req->input('comments_enabled') ?? 0
        ]);
        if ($id) {
            return response()->json(['message' => 'Created successfully'], 200);
        }
        return response()->json(['message' => 'Something went wrong'], 400);
    }

    public function listFeed(AdminRequest $req)
    {
        $data = DB::table('app_feed')->get();
        return [
            $data
        ];
    }

    public function updateFeed(AdminRequest $req, $feed_id)
    {
        DB::transaction();
        try {
            DB::table('app_feed')
                ->where('feed_id', $feed_id)
                ->update([
                    'comments_enabled' => $req->input('comments_enabled')
                ]);
            return response()->json(['message' => 'Updated'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Something went wrong'], 400);
        }
    }


    public function getCommentsReport(AdminRequest $req, PostRepository $postrepo)
    {

        $startDate = $req->input('startDate');
        $endDate = $req->input('endDate');

        return $postrepo->getCommentsReport($startDate, $endDate);
    }

    public function commentAction(AdminRequest $req, PostRepository $postrepo)
    {
        $entity_id = $req->input('entity_id');
        $comment_id = $req->input('comment_id');
        //do the action
        DB::beginTransaction();
        try {
            $entity_id = $req->input('entity_id');
            $comment_id = $req->input('comment_id');
            $review_status = $req->input('review_status');
            $reviewed_by = $req->input('reviewed_by');
            $reviewed_on = Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s');

            //create a report first
            if (!in_array($review_status, ["Unpin", "Pin"])){
                $postrepo->reportComment($entity_id, $comment_id, "A_1", 0, -1);
            }

            $tableName = $postrepo->getTableName($entity_id)[0];
            //new logic
            // deleted_at -1 = hidden from the dashboard
            // deleted_at timestamp = deleted by own user
            if ($review_status == "Hide") {
                $deleted_at = -1;
                $check = DB::table($tableName)->where('id', $comment_id)->first();
                //this checking is to make sure if user already deleted this comment, we retain the timestamp of deleted at made by the user
                if (!$check) {
                    throw new Exception("Comment does not exists", 400);
                }
                if ($check->deleted_at != null) {
                    $deleted_at = $check->deleted_at;
                }
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['deleted_at' => $deleted_at]);
            } else if ($review_status == "Unhide" || $review_status == "Ignore") {
                $deleted_at = null;
                $check = DB::table($tableName)->where('id', $comment_id)->first();
                if (!$check) {
                    throw new Exception("Comment does not exists", 400);
                }
                if ($check->deleted_at != null) {
                    $deleted_at = $check->deleted_at == -1 ? null : $check->deleted_at;
                }
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['deleted_at' => $deleted_at]);
            } else if ($review_status == "Pin") {
                $pinned_at = time();
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['pin' => $pinned_at]);
            } else if ($review_status == "Unpin") {
                $pinned_at = 0;
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['pin' => $pinned_at]);
            }

            //update report/pin status
            if ($review_status == "Pin" || $review_status == "Unpin") {
                // DB::table('comment_pins')
                //     ->updateOrInsert(
                //     ['comment_id' => $comment_id, 'entity_id' => $entity_id],
                //     ['pinned_by' => $reviewed_by,'pin_status' => $review_status]
                // );
                //had to do this since updateOrInsert doesnt work with unique key of comment_id + entity_id
                $q = "INSERT IGNORE INTO comment_pins
                    (comment_id, entity_id, pinned_by, pin_status)
                    values ($comment_id, '$entity_id', '$reviewed_by', '$review_status')
                    on duplicate key update pinned_by = values(pinned_by), pin_status = values(pin_status)";
                DB::statement($q);
            } else {
                DB::table('comment_reports')
                    ->where('entity_id', $entity_id)
                    ->where('comment_id', $comment_id)
                    ->update(['processed' => 1, 'review_status' => $review_status, 'reviewed_by' => $reviewed_by, 'reviewed_on' => $reviewed_on]);
            }

            //return ['message' => 'success'];

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'success'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function commentReportAction(AdminRequest $req, PostRepository $postrepo)
    {
        DB::beginTransaction();
        try {
            $entity_id = $req->input('entity_id');
            $comment_id = $req->input('comment_id');
            $review_status = $req->input('review_status');
            $reviewed_by = $req->input('reviewed_by');
            $reviewed_on = Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s');
            $startDate = $req->input('startDate');
            $endDate = $req->input('endDate');


            $tableName = $postrepo->getTableName($entity_id)[0];
            //new logic
            // deleted_at -1 = hidden from the dashboard
            // deleted_at timestamp = deleted by own user
            if ($review_status == "Hide") {
                $deleted_at = -1;
                $check = DB::table($tableName)->where('id', $comment_id)->first();
                //this checking is to make sure if user already deleted this comment, we retain the timestamp of deleted at made by the user
                if (!$check) {
                    throw new Exception("Comment does not exists", 400);
                }
                if ($check->deleted_at != null) {
                    $deleted_at = $check->deleted_at;
                }
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['deleted_at' => $deleted_at]);
            } else if ($review_status == "Unhide" || $review_status == "Ignore") {
                $deleted_at = null;
                $check = DB::table($tableName)->where('id', $comment_id)->first();
                if (!$check) {
                    throw new Exception("Comment does not exists", 400);
                }
                if ($check->deleted_at != null) {
                    $deleted_at = $check->deleted_at == -1 ? null : $check->deleted_at;
                }
                DB::table($tableName)
                    ->where('id', $comment_id)
                    ->update(['deleted_at' => $deleted_at]);
            }

            //update report status
            DB::table('comment_reports')
                ->where('entity_id', $entity_id)
                ->where('comment_id', $comment_id)
                ->update(['processed' => 1, 'review_status' => $review_status, 'reviewed_by' => $reviewed_by, 'reviewed_on' => $reviewed_on]);
            //return ['message' => 'success'];
            //get updated data
            $reports = $postrepo->getCommentsReport($startDate, $endDate);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'success',
                'reports' => $reports
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    function paginateWithoutKey($items, $perPage = 15, $page = null, $options = [])
    {

        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);

        $items = $items instanceof Collection ? $items : Collection::make($items);

        $lap = new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);

        return [
            'current_page' => $lap->currentPage(),
            'data' => $lap->values(),
            'first_page_url' => $lap->url(1),
            'from' => $lap->firstItem(),
            'last_page' => $lap->lastPage(),
            'last_page_url' => $lap->url($lap->lastPage()),
            'next_page_url' => $lap->nextPageUrl(),
            'per_page' => $lap->perPage(),
            'prev_page_url' => $lap->previousPageUrl(),
            'to' => $lap->lastItem(),
            'total' => $lap->total(),
        ];
    }

    public function banUser(BanCommentUserRequest $req)
    {
        $forever = $req->getForeverStatus();
        $days = $req->getBannedDays();
        $user_id = $req->getUserId();
        $adminUser = $this->getAdminUser();
        $data = ['user_id' => $user_id, 'banned_by' => $adminUser->id, 'created_at' => time()];

        $forever ? $data['forever'] = $forever : $data['ban_end_date'] = Carbon::now('Asia/Kuala_Lumpur')->addDays($days)->format('Y-m-d H:i:s');
        DB::beginTransaction();
        try {
            DB::table('banned_users')->insert($data);
            $user = $this->getUser($user_id);
            $this->logUserAction($user_id, $adminUser->id, 'Blocked');
            $user->isBlocked = 'Blocked';
            DB::table('userLogin')->where('id', $user_id)->update(['banned' => Carbon::now()]);
            DB::commit();
            return response()->json(['message' => 'User is successfully banned from commenting', 'user' => $user]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 400);
            notify_now($e);
            return response()->json(['message' => 'Failed to ban user', 'user' => [$user]], 400);
        }
    }

    public function unbanUser(BanCommentUserRequest $req)
    {
        $user_id = $req->getUserId();
        $adminUser = $this->getAdminUser();
        if (!$banned = DB::table('banned_users')->where('user_id', $user_id)->whereNull('deleted_at')->first()) {
            return response()->json(['message' => 'User is not blocked from commenting']);
        }
        DB::beginTransaction();
        try {
            DB::table('banned_users')->where('id', $banned->id)->update([
                'unbanned_by' => $adminUser->id,
                'deleted_at' => time()
            ]);
            $user = $this->getUser($user_id);
            $this->logUserAction($user_id, $adminUser->id, 'Unblocked');
            $user->isBlocked = 'Blocked';
            //update userLogin banned field
            DB::table('userLogin')->where('id', $user_id)->update(['banned' => null]);
            DB::commit();
            return response()->json(['message' => 'User is unblocked from commenting', 'user' => [$user]]);
        } catch (\Exception $e) {
            DB::rollBack();
            notify_now($e);
            return response()->json(['message' => 'Failed to unban user', 'user' => [$user]], 400);
        }
    }

    public function verifyUser(VerifyUserRequest $req)
    {
        $user_id = $req->getUserId();
        $adminUser = $this->getAdminUser();
        DB::beginTransaction();
        try {
            $user = $this->getUser($user_id);
            if (!$user){
                throw new Exception('User Not Found');
            }
            DB::table('user_comment_link_whitelists')->updateOrInsert(
                ['user_id' => $user_id],
                ['deleted_at' => null]
            );
            $user->isVerified = 'Yes';
            $this->logUserAction($user_id, $adminUser->id, 'Verified');
            DB::commit();
            return response()->json(['message' => 'User is verified', 'user' => [$user]]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 400);
            notify_now($e);
        }
    }

    public function unverifyUser(VerifyUserRequest $req)
    {
        $user_id = $req->getUserId();
        $adminUser = $this->getAdminUser();
        DB::beginTransaction();
        try {
            $user = $this->getUser($user_id);
            DB::table('user_comment_link_whitelists')->updateOrInsert(
                ['user_id' => $user_id],
                ['deleted_at' => Carbon::now()]
            );
            $user->isVerified = 'No';
            $this->logUserAction($user_id, $adminUser->id, 'Unverified');
            DB::commit();
            return response()->json(['message' => 'User is verified', 'user' => [$user]]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 400);
            notify_now($e);
        }
    }

    private function getUser($user_id){
        $user = DB::table('userLogin')->where('id', $user_id)->first();
        if (!$user){
            throw new Exception('User Not Found');
        }
        $ios_profiles = DB::table('profile')->where('firebaseID', $user->loginProviderUID)->get()->keyBy('firebaseID');
        $and_profiles = DB::table('android_profile')->where('firebaseID', $user->loginProviderUID)->get()->keyBy('firebaseID');
        $banned = DB::table('banned_users')->where('user_id', $user_id)->whereNull('deleted_at')->get()->keyBy('user_id');
        $whitelisted = DB::table('user_comment_link_whitelists')->where('user_id', $user_id)->whereNull('deleted_at')->get()->keyBy('user_id');
        $user->isBlocked = $banned->get($user->id) ? 'Blocked' : '-';
        $user->isVerified = $whitelisted->get($user->id) ? 'Yes' : 'No';
        $ios = $ios_profiles->get($user->loginProviderUID);
        $and = $and_profiles->get($user->loginProviderUID);
        if(!empty($ios)){
            $user->profile_id = 'i_' . $ios->id;
        }else if(!empty($and)){
            $user->profile_id = 'a_' . $and->id;
        }
        return $user;
    }

    private function logUserAction($userId, $adminId, $action): void{
        DB::table('user_action_logs')->insert([
            'user_id' => $userId,
            'admin_id' => $adminId,
            'action' => $action
        ]);
    }

    public function listBanUsers(AdminRequest $req)
    {
        $banned = DB::table('banned_users')->whereNull('deleted_at')->get();
        $ids = $banned->pluck('user_id');

        $userData = DB::table('userLogin')->whereIn('id', $ids)->get()->keyBy('id');
        $banned->map(function ($item) use (&$userData) {
            $item->username = $userData->get($item->user_id)->loginDisplayName ?? "Anonymous User " . $item->user_id;
            $item->email = $userData->get($item->user_id)->loginEmail ?? "email" . $item->user_id . "@example.org";
            return $item;
        });

        return response()->json($banned);
    }

    public function banHistory($id)
    {
        $history = DB::table('banned_users')->where('user_id', $id)->orderByDesc('created_at')->get();
        $banAdminIds = $history->pluck('banned_by')->toArray();
        $unbanAdminIds = $history->pluck('unbanned_by')->toArray();
        $adminIds = array_merge($banAdminIds, $unbanAdminIds);
        $admins = DB::connection('admin_cluster')->table('users')->whereIn('id', $adminIds)->get()->keyBy('id');
        $history->map(function ($item) use ($admins) {
            $banned = $admins->get($item->banned_by)->name ?? "";
            $unbanned = $admins->get($item->unbanned_by)->name ?? "";
            $item->banned_by = $banned != "" ? $banned . " (" . $item->banned_by . ")" : "";
            $item->unbanned_by = $unbanned != "" ? $unbanned . " (" . $item->unbanned_by . ")" : "";
            return $item;
        });
        return $history;
    }

    public function userActionHistory($id)
    {
        $history = DB::table('user_action_logs')->where('user_id', $id)->orderByDesc('created_at')->paginate(Constants::DEFAULT_ITEMS_PER_PAGE);
        $adminIds = collect($history->items())->pluck('admin_id')->toArray();
        $admins = DB::connection('admin_cluster')->table('users')->whereIn('id', $adminIds)->get()->keyBy('id');
        $history->getCollection()->transform(function ($item) use ($admins) {
            $admin = $admins->get($item->admin_id);
            $item->admin = "$admin->name ($admin->id)";
            return $item;
        });
        return $history;
    }

    public function getUsers(AdminGetUsersRequest $req)
    {
        $user_id = $req->getID();
        $email = $req->getEmail();
        $name = $req->getLoginName();
        $blocked = $req->getIsBlocked();
        $verified = $req->getIsVerified();
        $username = $req->getUsername();
        $banned = DB::table('banned_users')->whereNull('deleted_at')->get()->keyBy('user_id');
        $whitelistedUsers = DB::table('user_comment_link_whitelists')->whereNull('deleted_at')->get()->keyBy('user_id');
        $whiteIds = $whitelistedUsers->pluck( 'user_id')->toArray();
        $bannedIds = $banned->pluck('user_id')->toArray();

        $users = DB::table('userLogin')
            ->leftJoin('user_profile', 'userLogin.id', '=', 'user_profile.user_id')
            ->select('userLogin.*', 'user_profile.username')
            ->when($verified, function ($query) use ($whiteIds) {
                return $query->whereIn('userLogin.id', $whiteIds);
            }, function($query) use($verified, $whiteIds){
                if($verified === false){
                    return $query->whereNotIn('userLogin.id', $whiteIds);
                }
                return $query;
            })
            ->when($blocked, function ($query) use ($bannedIds) {
                return $query->whereIn('userLogin.id', $bannedIds);
            }, function ($query) use ($blocked, $bannedIds){
                if($blocked === false){
                    return $query->whereNotIn('userLogin.id', $bannedIds);
                }
                return $query;
            })
            ->when($user_id, function ($query) use (&$user_id) {
                return $query->where('userLogin.id', 'LIKE', "%{$user_id}%");
            })
            ->when($email, function ($query) use (&$email) {
                return $query->where('userLogin.loginEmail', 'LIKE', "%{$email}%");
            })
            ->when($name, function ($query) use (&$name) {
                $names = explode(' ', $name);
                foreach ($names as $item) {
                    $check = trim($item);
                    $query->where('loginDisplayName', 'LIKE', "%{$check}%");
                }
                return $query;
            })
            ->when($username, function ($query) use (&$username) {
                return $query->where('user_profile.username', 'LIKE', "%{$username}%");
            })
            ->orderByDesc('id')
            ->paginate(Constants::DEFAULT_ITEMS_PER_PAGE);
        $uids = collect($users->items())->pluck('loginProviderUID')->toArray();
        $ios_profiles = DB::table('profile')->whereIn('firebaseID', $uids)->get()->keyBy('firebaseID');
        $and_profiles = DB::table('android_profile')->whereIn('firebaseID', $uids)->get()->keyBy('firebaseID');
        $users->map(function ($item) use (&$banned, $ios_profiles, $and_profiles, $whitelistedUsers) {
            $item->isBlocked = $banned->get($item->id) ? 'Blocked' : '-';
            $item->isVerified = $whitelistedUsers->get($item->id) ? 'Yes' : 'No';
            $ios = $ios_profiles->get($item->loginProviderUID);
            $and = $and_profiles->get($item->loginProviderUID);
            if(!empty($ios)){
                $item->profile_id = 'i_' . $ios->id;
            }else if(!empty($and)){
                $item->profile_id = 'a_' . $and->id;
            }
            return $item;
        });

        return response()->json($users);
    }

    public function pinEntity(PinRequest $req)
    {

        $entityid = $req->entity_id;
        $language = $req->language;
        $timeout = $req->timeout;
        $create_date = Carbon::now();

        $langarray = ['en', 'zh', 'ms', 'all'];


        //validate language
        if (!in_array($language, $langarray)) {
            return response()->json([
                'status' => 'failed',
                'message' => 'invalid language'
            ], 400);
        }

        //validate timeout format
        if (!strtotime($timeout)) {
            return response()->json([
                'status' => 'failed',
                'message' => 'invalid date format for timeout'
            ], 400);
        }

        //default to article
        $tableName = 'pinned_articles';
        $columnName = 'article_id';
        //check if video
        if (like_match("V%_%", $entityid)) {
            $tableName = 'pinned_videos';
            $columnName = 'video_id';
        }

        //send to db

        try {
            DB::table($tableName)
                ->updateOrInsert(
                    [$columnName => $entityid, 'language' => $language],
                    ['timeout' => $timeout]
                );

            $data = [
                'entity_id' => $entityid,
                'language' => $language,
                'created_date' => $create_date,
                'timeout' => $timeout
            ];

            return response()->json([
                'status' => 'success',
                'message' => $data
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function hideResource(HideArticleRequest $req, $id, PublisherRepository $prepo)
    {
        $type = 'Article';
        $updateColumn = "updated_at";
        DB::beginTransaction();
        try {
            if (like_match("V%_%", $id)) {
                $type = 'Video';
                $table = 'videos';
                $delete_column = 'deleted_at';
                $delete_value = time();
                $update_value = $delete_value;
                $entity_type = "v";
            } elseif (like_match("P%_%", $id)) {
                $type = 'Podcast';
                $table = 'podcasts';
                $delete_column = 'deleted_at';
                $delete_value = time();
                $update_value = $delete_value;
                $entity_type = "p";
            } elseif (like_match("M%", $id)) {
                $type = 'Mikrowav';
                $table = 'mikrowavs';
                $delete_column = 'is_flagged';
                $delete_value = true;
                $updateColumn = "flagged_at";
                $update_value = Carbon::now()->format('Y-m-d H:i:s');
                $entity_type = "m";
            } else {
                $table = 'predictions';
                $delete_column = 'language';
                $delete_value = "DEL";
                $update_value = Carbon::now()->format('Y-m-d H:i:s');
                $entity_type = "a";
            }

            /** @var ReportRepository $reportRepo */
            $reportRepo = app(ReportRepository::class);
            /** @var PostRepository $postRepo */
            $postRepo = app(PostRepository::class);
            //create dashboard action for reference in reported content dashboard
            $reportRepo->reportEntity($id, "admin",  "A_1", 11);
            $adminUser = $this->getAdminUser();
            $actionBy = $adminUser->identity . " (" . $adminUser->id . ")";
            DB::table('reports')
                ->where('entity_type', $entity_type)
                ->where('entity_id', $id)
                ->update(['review_status' => "Hide", 'reviewed_by' => $actionBy, 'reviewed_on' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')]);

            $postRepo->updateContentStatus($table, $id, $entity_type, $delete_column, $delete_value, $updateColumn, $update_value, $adminUser);

            DB::commit();
            event(new AdminChangeContentStatusEvent($id, false, 'DEL', $adminUser));
            $this->clearContentCache($id);
            return response()->json(['msg' => "{$type} {$id} is hidden"]);
        } catch (\Throwable $e) {
            DB::rollBack();
            return response()->json(['msg' => "$e"], 400);
            return response()->json(['msg' => "Something went wrong, in hiding {$type}"], 400);
        }
    }

    public function showResource(HideArticleRequest $req, $id, PublisherRepository $prepo)
    {

        $type = 'Article';
        $updateColumn = "updated_at";
        DB::beginTransaction();
        try {
            if (like_match("V%_%", $id)) {
                $type = 'Video';
                $table = 'videos';
                $show_column = 'deleted_at';
                $show_value = null;
                $update_value = time();
                $entity_type = "v";
            } elseif (like_match("P%_%", $id)) {
                $type = 'Podcast';
                $table = 'podcasts';
                $show_column = 'deleted_at';
                $show_value = null;
                $update_value = time();
                $entity_type = "p";
            } elseif (like_match("M%", $id)) {
                $type = 'Mikrowav';
                $table = 'mikrowavs';
                $updateColumn = "flagged_at";
                $show_column = 'is_flagged';
                $show_value = false;
                $update_value = null;
                $entity_type = "m";
            } else {
                $table = 'predictions';
                $show_column = 'language';
                $entity_type = "a";
                $update_value = Carbon::now()->format('Y-m-d H:i:s');
                $channel_id = DB::table($table)->where('unique_id', $id)->get()->pluck('channel_id')->toArray();
                $show_value = DB::table('channels')->where('id', $channel_id)->first()->language;
            }

            $adminUser = $this->getAdminUser();
            //check if partner can unhide the items or not
            if (isset($adminUser->request_type) && $adminUser->request_type === "partner" && !Str::endsWith($adminUser->identity, '@newswav.com')){
                $disabled = DB::table('disabled_contents')->where('unique_id', $id)->first();
                if ($disabled && !isPartner($disabled->hidden_by)){
                    return response()->json(['msg' => "This content has been flagged and hidden. <NAME_EMAIL> for assistance."], 401);
                }
            }

            $reportRepo = app(ReportRepository::class);
            /** @var PostRepository $postRepo */
            $postRepo = app(PostRepository::class);
            //create dashboard action for reference in reported content dashboard
            $reportRepo->reportEntity($id, "admin",  "A_1", 11);
            $actionBy = $adminUser->identity . " (" . $adminUser->id . ")";
            DB::table('reports')
                ->where('entity_type', $entity_type)
                ->where('entity_id', $id)
                ->update(['review_status' => "Unhide", 'reviewed_by' => $actionBy, 'reviewed_on' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')]);

            $postRepo->updateContentStatus($table, $id, $entity_type, $show_column, $show_value, $updateColumn, $update_value, $adminUser);

            DB::commit();
            event(new AdminChangeContentStatusEvent($id, true, $entity_type === 'a' ? $show_value : null, $adminUser));
            return response()->json(['msg' => "{$type} {$id} is shown"]);
        } catch (\Throwable $e) {
            DB::rollBack();
            return response()->json(['msg' => "$e"], 400);
            return response()->json(['msg' => "Something went wrong, in showing {$type}"], 400);
        }
    }

    public function commentToggle($uniqueId)
    {
        try {
            $updatedAt = now();

            if (like_match("M%", $uniqueId)) {
                $type = 'Mikrowav';
                $table = 'mikrowavs';
                $column = 'is_comment_disabled';
            } else {
                return response()->json(['msg' => "Invalid unique ID"], 404);
            }

            // Retrieve the current value of the column
            $currentValue = DB::table($table)
                ->where('unique_id', $uniqueId)
                ->value($column);

            // Toggle the value
            $newValue = !$currentValue;

            // Update the column with the new value
            DB::table($table)
                ->where('unique_id', $uniqueId)
                ->update([
                    $column => $newValue,
                    'updated_at' => $updatedAt,
                ]);

            $status = $newValue ? 'disabled' : 'enabled';
            return response()->json(['msg' => "{$type} {$uniqueId} comments are now {$status}"]);
        } catch (\Throwable $e) {
            return response()->json(['msg' => "$e"], 400);
        }
    }

    public function getPinned(PostRepository $postrepo)
    {
        $pinned = $postrepo->getPinned();
        return response()->json($pinned);
    }


    public function pinEntity2(PinRequest2 $req, ArticleRepository $repo, PostRepository $postrepo)
    {

        $entityId = $req->unique_id;
        $language = $req->language;
        $feed = $req->feed;
        $scheduled = $req->scheduled ?? 0;
        $start = $req->start;
        $end = $req->end;
        $matchSubLang = $req->matchSubLang ?? 0;
        $type = $req->type;

        if ($type == "article") {
            try {
                $check = $repo->getArticle($entityId);
            } catch (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException $e) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId doesn't exist"
                ], 400);
            }

            // if (!$check) {
            //     return response()->json([
            //         'status' => 'failed',
            //         'message' => "$entityId does not exist"
            //     ], 400);
            // }

            if ($check->deleted == 1) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId is currently hidden."
                ], 400);
            }
        }

        if ($type == "video") {

            $check = DB::table('videos')->where('unique_id', $entityId)->first();

            if (!$check) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId doesn't exist"
                ], 400);
            }

            if (!is_null($check->deleted_at)) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId is currently hidden"
                ], 400);
            }
        }

        if ($type == "podcast") {

            $check = DB::table('podcasts')->where('unique_id', $entityId)->first();

            if (!$check) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId doesn't exist"
                ], 400);
            }

            if (!is_null($check->deleted_at)) {
                return response()->json([
                    'status' => 'failed',
                    'message' => "$entityId is currently hidden"
                ], 400);
            }
        }

        //check feed
        $feed = $feed['id'] ?? -1;



        if ($feed == -1) {
            return response()->json([
                'status' => 'failed',
                'message' => "invalid feed id"
            ], 400);
        }

        //for bq error
        // $tempFeed = explode("_", $feed);
        // $req->merge(['feed' => $tempFeed[2]]);
        if ($type === "highlight"){
            $req->merge(['feed' => 0]);
        } else {
            $tempFeed = explode("_", $feed);
            $req->merge(['feed' => $tempFeed[2]]);
        }



        //get currently pinned item
        $now = Carbon::now()->format('Y-m-d H:i:s');
        $check = DB::table('pinned')
            ->whereNotNull('start')
            ->where('end', '>', $now)
            ->get();

        //check unique id if it's already pinned
        if ($check->where('unique_id', $entityId)->first()) {
            $utype = ucwords($type);
            return response()->json([
                'status' => 'failed',
                'message' => "$utype ID $entityId is scheduled or currently pinned. Please check"
            ], 400);
        }

        //check if the feed currently have 2 active pinned items
        // if (count($check->where('feed', $feed)->toArray()) >= 2) {
        //     return response()->json([
        //         'status' => 'failed',
        //         'message' => "Selected feed currently have maxiumum pinned $type"
        //     ], 400);
        // } //remove feed pin checking


        $language = implode(",", $language);
        $req->merge(['language' => $language]);



        $start = Carbon::parse($start);
        $end = Carbon::parse($end);

        /** @var PinItemService $pinService */
        $pinService = app(PinItemService::class);
        $enabledFeeds = $pinService->getPinEnabledFeeds();

        //default to article
        $tableName = 'pinned';
        $columnName = 'unique_id';


        $entity = "Article";
        if ($type == "video") {
            $entity = "Video";
        }

        //skip highlight check, allow all type
        if ($type !== "highlight"){
            if (like_match("V%_%", $entityId)) {
                if (!in_array($feed, $enabledFeeds->video->feedIds)) {
                    return response()->json([
                        'status' => 'failed',
                        'message' => 'invalid feed for video'
                    ], 400);
                }
            } else if (like_match("P%_%", $entityId)) {
                if (!in_array($feed, $enabledFeeds->podcast->feedIds)) {
                    return response()->json([
                        'status' => 'failed',
                        'message' => 'invalid feed for podcast'
                    ], 400);
                }
            } else {
                if (!in_array($feed, $enabledFeeds->article->feedIds)) {
                    return response()->json([
                        'status' => 'failed',
                        'message' => 'invalid feed for article'
                    ], 400);
                }
            }
        }





        //send to db
        DB::beginTransaction();
        try {

            DB::table($tableName)
                ->insert(
                    [$columnName => $entityId, 'feed' => $feed, 'type' => $type, 'start' => $start, 'end' => $end, 'matchSubLang' => $matchSubLang, 'language' => $language, 'scheduled' => $scheduled]
                );


            $data = [
                'entity_id' => $entityId,
                'language' => $language,
                'match_sub_lang' => $matchSubLang,
                'feed' => $feed,
                'start' => $start,
                'end' => $end
            ];

            $pinned = $postrepo->getPinned();
            DB::commit();
            try {
                $feedSlug = app(FeedRepository::class)->getFeedSlugByFeedId($feed);
                event(new AdminPinContentEvent($entityId, $feedSlug, $start->toIso8601ZuluString(), $end->toIso8601ZuluString(), explode(",", $language), (bool)$matchSubLang, $this->getAdminUser()));
            } catch (\Exception $e){
                notify_now($e);
            }

            return response()->json([
                'status' => 'success',
                'message' => "$entity ID $entityId is successfully scheduled or pinned!",
                'data' => $data,
                'pinned' => $pinned
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            $data = [
                'entity_id' => $entityId,
                'language' => $language,
                'match_sub_lang' => $matchSubLang,
                'feed' => $feed,
                'start' => $start,
                'end' => $end
            ];
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage(),
                'data' => $data
            ], 400);
        }
    }

    public function getPinFeeds(){
        /** @var PinItemService $pinService */
        $pinService = app(PinItemService::class);
        $object = $pinService->getPinEnabledFeeds();
        return response()->json($object);
    }

    public function editPinEntity(PinRequest2 $req, $type, $id, PostRepository $postrepo)
    {
        $entityId = $req->unique_id;
        $language = $req->language;
        $feed = $req->feed;
        $scheduled = $req->scheduled ?? 0;
        $start = $req->start;
        $end = $req->end;
        $matchSubLang = $req->matchSubLang ?? 0;

        $language = implode(",", $language);
        $feed = $feed['id'] ?? -1;
        //for bq error
        // $tempFeed = explode("_", $feed);
        // $req->merge(['feed' => $tempFeed[2]]);
        if ($type === "highlight"){
            $req->merge(['feed' => 0]);
        } else {
            $tempFeed = explode("_", $feed);
            $req->merge(['feed' => $tempFeed[2]]);
            Log::info($tempFeed[2]);
        }

        $start = Carbon::parse($start);
        $end = Carbon::parse($end);

        $tableName = "pinned";
        $entity = "Article";
        if ($type == "video") {
            $entity = "Video";
        }
        DB::beginTransaction();
        try {
            $data = DB::table($tableName)
                ->where('id', $id)
                ->first();


            $entityId = $data->unique_id;

            DB::table($tableName)
                ->where('id', $id)
                ->update(['start' => $start, 'end' => $end, 'matchSubLang' => $matchSubLang, 'language' => $language, 'scheduled' => $scheduled, 'feed' => $feed]);

            $pinned = $postrepo->getPinned();
            DB::commit();
            try {
                $feedSlug = app(FeedRepository::class)->getFeedSlugByFeedId($feed);
                event(new AdminPinContentEvent($entityId, $feedSlug, $start->toIso8601ZuluString(), $end->toIso8601ZuluString(), explode(",", $language), (bool)$matchSubLang, $this->getAdminUser()));
            } catch (\Exception $e){
                notify_now($e);
            }
            return response()->json([
                'status' => 'success',
                'message' => "$entity ID $entityId is successfully scheduled or pinned!",
                'pinned' => $pinned
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function unpinEntity($type, $id)
    {
        //return response()->json([$type, $id]);

        $tableName = "pinned";
        $entity = "Article";
        if ($type == "video") {
            $entity = "Video";
        }


        try {
            $data = DB::table($tableName)
                ->where('id', $id)
                ->first();

            $startvalue = null;
            $start = Carbon::parse($data->start);
            $now = Carbon::now();
            if ($now >= $start) {
                $startvalue = $start->toDateTimeString();
            }


            $entityId = $data->unique_id;
            $feed = $data->feed;

            DB::beginTransaction();

            DB::table($tableName)
                ->where('id', $id)
                ->update(['start' => $startvalue, 'end' => Carbon::now()->toDateTimeString()]);

            DB::commit();

            try {
                $feedSlug = app(FeedRepository::class)->getFeedSlugByFeedId($feed);
                event(new AdminUnpinContentEvent($entityId, $feedSlug, $this->getAdminUser()));
            } catch (\Exception $e){
                notify_now($e);
            }

            return response()->json([
                'status' => 'success',
                'message' => "$entity ID $entityId is successfully unpinned!"
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage(),
            ], 400);
        }
    }


    function cancelPNSend(PnCancelRequest $req, ScheduledPnService $service)
    {
        $baseId = explode('_', $req->getArticleID(), 2);
        $unique_id = end($baseId);
        $tables = collect(DB::connection('pn_cluster')->select('SHOW TABLES'))->filter(function ($item) use ($unique_id) {
            return false !== stripos($item->{"Tables_in_".config('database.connections.pn_cluster.database')}, $unique_id);
        });

        if ($tables->isEmpty()) {
            return response()->json([
                'status' => 'failed',
                'message' => "$unique_id PN table not found. Please try again"
            ], 400);
        }

        //return $tables->toArray();

        try {
            foreach ($tables as $table) {
                $new = $table->{"Tables_in_".config('database.connections.pn_cluster.database')} . '_cancel';
                if (Str::contains($table->{"Tables_in_".config('database.connections.pn_cluster.database')}, 'actions')) {
                    $new = str_replace('actions', 'cancelA', $table->{"Tables_in_".config('database.connections.pn_cluster.database')});
                }
                if (Str::contains($table->{"Tables_in_".config('database.connections.pn_cluster.database')}, 'receivers')) {
                    $new = str_replace('receivers', 'cancelR', $table->{"Tables_in_".config('database.connections.pn_cluster.database')});
                }
                $pntable = "Tables_in_".config('database.connections.pn_cluster.database');
                if (!Str::contains($table->{"Tables_in_".config('database.connections.pn_cluster.database')}, 'cancel')) {
                    DB::connection('pn_cluster')->statement(
                        "ALTER TABLE " . $table->{"$pntable"} . " RENAME TO $new;"
                    );
                }
            }
            $service->logPn($req);
        } catch (Exception $e) {
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage(),
            ], 400);
        }

        return response()->json([
            'status' => 'success',
            'message' => "$unique_id PN canceled successfully"
        ], 200);
    }

    public function checkPeriod()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $day = Carbon::now()->format('d');
        $hour = Carbon::now()->format('H');
        $period = $this->getPeriod($hour);
        $table_name = "{$year}_{$month}_{$day}_p_{$period}";
        $table = collect(DB::connection('pn_cluster')->select('SHOW TABLES'))->filter(function ($item) use ($table_name) {
            return false !== Str::contains($item->{"Tables_in_".config('database.connections.pn_cluster.database')}, $table_name);
        });
        if ($table->isEmpty()) {
            return response()->json([
                'message' => "Ok to trigger"
            ], 200);
        }
        return response()->json([
            'message' => 'Not ok to trigger'
        ], 400);
    }

    private function getPeriod($hour)
    {
        $period = 0;
        switch ($hour) {
            case 7:
            case 9:
            case 10:
            case 11:
                $period = 1;
                break;
            case 12:
            case 13:
            case 14:
                $period = 2;
                break;
            case 15:
            case 16:
            case 17:
                $period = 3;
                break;
            case 18:
            case 19:
            case 20:
                $period = 4;
                break;
            case 21:
            case 22:
            case 23:
                $period = 6;
                break;
        }

        return $period;
    }

    public function getSocialFeed()
    {
        $data = DB::table('feeds')->orderByDesc('publishedDate')->simplePaginate(20);
        $media = array();

        $data->getCollection()->each(function ($item, $key) use (&$media) {
            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                if (count($medias) > 0) {
                    array_push($media, ...$medias);
                }
            }
        });

        $mediaData = DB::table('media')
            ->select('id', 'caption', 'url', 'originalUrl')
            ->whereIn('id', $media)
            ->get()
            ->keyBy('id');

        $data->getCollection()->transform(function ($item) use ($mediaData) {

            if ($item->media && $item->media != '') {
                $medias = explode(',', $item->media);
                $medias = array_unique($medias);
                foreach ($medias as $m) {
                    if ($m && strlen($m) > 0) {
                        $media = $mediaData->get($m);
                        $item->mediaArray[] = [
                            'id' => $media->id,
                            'url' => $media->url,
                            //'compressUrl' => $media->originalUrl
                        ];
                        break;
                    }
                }
            }
            return $item;
        });
        return $data;
    }

    /**
     * @param string $fbUrl
     * @return array|string|string[]
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getFetchRSSFeed(string $fbUrl)
    {
        $client = new \GuzzleHttp\Client();
        app()->configure('fetcherss');
        $apiKey = config('fetcherss.api_key');

        $res = $client->request('GET', "http://fetchrss.com/api/v1/feed/create?auth=$apiKey&url=$fbUrl");
        if ($res->getStatusCode() > 200) {
            Log::info($res->getBody());
            abort(500, "unexpected error from fetcherss " . $res->getStatusCode());
        }
        $res = json_decode($res->getBody());
        $feedUrl = $res->feed->rss_url;
        $feedUrl = str_replace(".xml", ".json", $res->feed->rss_url);
        return $feedUrl;
    }

    public function getCommentsDashboard(AdminRequest $req, PostRepository $postrepo)
    {

        $startDate = $req->input('startDate');
        $endDate = $req->input('endDate');
        $page = $req->input('page') ?? 1;
        $query = $req->input('comment') ?? "*";
        $commentby = $req->input('commentby') ?? false;
        $source = $req->input('source') ?? "*";
        $type = $req->input('type') ?? "*";


        return $postrepo->getCommentsDashboard($startDate, $endDate, $page, $query, $commentby, $source, $type);
    }

    public function getContentFiltersByDate(AllContentRequest $req, ArticleRepository $arepo, TopicRepository $trepo, PublisherRepository $prepo, VideoRepository $vrepo, PodcastRepository $podrepo)
    {
        $hours = $req->input('hours') ?? 1; //if somehow not sent by dashboard, default hours is 2.
        $type = $req->input('type_array') ?? null;
        $native = $req->input('native') ?? null;
        $type = $type ? explode(",", $type) : ['Articles'];

        $allItems = collect([]);

        if (in_array("Article", $type)) {
            $aquery = $arepo->getArticlesPublishersOrTopics($hours, $native);
            $apts = $aquery->get();
            $allItems = $allItems->merge($apts);
        }

        if (in_array("Video", $type)) {
            $vquery = $vrepo->getVideosPublishersOrTopics($hours);
            $vpts = $vquery->get();
            $allItems = $allItems->merge($vpts);
        }

        if (in_array("Podcast", $type)) {
            $pquery = $podrepo->getPodcastsPublishersOrTopics($hours);
            $ppts = $pquery->get();
            $allItems = $allItems->merge($ppts);
        }


        $topics_id = array();
        $publishers_id = array();

        foreach ($allItems as $pt) {
            $topics_id[] = $pt->topic;
            $publishers_id[] = $pt->publisher_id;
        }

        $topics_id = array_values(array_unique($topics_id));
        $publishers_id = array_values(array_unique($publishers_id));
        $topics = $trepo->getTopics($topics_id)->sortBy('nameEN', SORT_NATURAL | SORT_FLAG_CASE)->map(function ($topic) {
            return collect($topic)
                ->only(['id', 'nameEN'])
                ->all();
        });
        $publishers = $prepo->getPublishers($publishers_id)->sortBy('publisherName', SORT_NATURAL | SORT_FLAG_CASE)->map(function ($topic) {
            return collect($topic)
                ->only(['id', 'publisherName'])
                ->all();
        });

        if (count($topics) == 0) {
            $topics->prepend(["id" => -998, 'nameEN' => '-']);
        }
        if (count($publishers) == 0) {
            $publishers->prepend(["id" => -998, 'publisherName' => '-']);
        }

        $topics = $topics->prepend(["id" => -999, 'nameEN' => 'All Topics']);
        $publishers = $publishers->prepend(["id" => -999, 'publisherName' => 'All Publishers']);

        return response()->json(['topics' => array_values($topics->toArray()), 'publishers' => array_values($publishers->toArray())]);
    }

    public function getContent(AllContentRequest $req, ArticleRepository $arepo, VideoRepository $vrepo, PodcastRepository $prepo)
    {
        $hours = filter_var($req->input('hours'), FILTER_VALIDATE_INT) ?: 2; //if somehow not sent by dashboard, default hours is 2.
        $topics = $req->input('topics_array') ?? null;
        $publishers = $req->input('publishers_array') ?? null;
        $language = $req->input('lang_array') ?? null;
        $type = $req->input('type_array') ?? null;
        $native = $req->input('native') ?? null;

        $topics = $topics ? explode(",", $topics) : [];
        $publishers = $publishers ? explode(",", $publishers) : [];
        $language = $language ? explode(",", $language) : [];
        $type = $type ? explode(",", $type) : [];
        if (in_array(-999, $topics) || in_array(-998, $topics)) {
            $topics = [];
        }
        if (in_array(-999, $publishers) || in_array(-998, $publishers)) {
            $publishers = [];
        }

        $allItems = collect([]);

        if (in_array("Article", $type)) {
            $article = $arepo->getContentArticles($hours, $publishers, $topics, $language, $native);
            $allItems = $allItems->merge($article);
        }

        if (in_array("Video", $type)) {
            $video = $vrepo->getContentVideos($hours, $publishers, $topics, $language);
            $allItems = $allItems->merge($video);
        }

        if (in_array("Podcast", $type)) {
             // get podcast data according to query (see src\app\Repositories\Podcast\PodcastRepositoryImpl.php)
             $podcast = $prepo->getContentPodcasts($hours, $publishers, $topics, $language, $native);
             $allItems = $allItems->merge($podcast);
         }

        // // print_r($allItems);
        // // die;
        // $ids = array_values($ids->pluck('entity_id')->toArray());
        // //to follow the ids order;
        // foreach ($ids as $key => $value){
        //     $ids[$key] = $allItems->where('unique_id', $value)->first();
        // }
        $allItems = $allItems->sortByDesc('published_date');
        return response()->json(array_values($allItems->toArray()));
    }

    public function getCommentsKeywords()
    {

        // $report = file_get_contents("https://cdn.newswav.com/comment_keywords/keywords.json");

        // $hide = file_get_contents("https://cdn.newswav.com/comment_keywords/keywords2.json");



        //     $report = ['value' =>  $report, 'type' => 'report'];


        //     $hide = ['value' =>  $hide, 'type' => 'hide'];


        // DB::table('auto_report_keywords')->insert($report);

        // DB::table('auto_report_keywords')->insert($hide);
        $hide = array();
        $report = array();
        DB::beginTransaction();


        try {
            //update report
            $hide = json_decode(DB::table('auto_report_keywords')->where('type', 'hide')->first()->value);

            $report = json_decode(DB::table('auto_report_keywords')->where('type', 'report')->first()->value);
        } catch (\Illuminate\Database\QueryException $e) {
            Log::emergency($e);
            abort(500, "Ops");
        }
        DB::commit();


        // return response()->json(['hide' => $hide, 'report' => $report]);



        return response()->json(['hide' => $hide, 'report' => $report]);
    }

    public function updateCommentsKeywords(CommentKeywordRequest $req)
    {


        $report = $req->input('report');
        $hide = $req->input('hide');
        $report = str_replace([", ", "，"], ",", $report);
        $hide = str_replace([", ", "，"], ",", $hide);
        $report = array_unique(explode(",", $report));
        $hide = array_unique(explode(",", $hide));
        usort($report, 'strnatcasecmp');
        usort($hide, 'strnatcasecmp');
        $report = json_encode($report);
        $hide = json_encode($hide);
        DB::beginTransaction();

        try {
            //update report
            DB::table('auto_report_keywords')->where('type', 'report')->update(['value' => $report]);
            //update hide
            DB::table('auto_report_keywords')->where('type', 'hide')->update(['value' => $hide]);
        } catch (\Illuminate\Database\QueryException $e) {
            Log::emergency($e);
            abort(500, "Ops");
        }

        DB::commit();

        DB::beginTransaction();

        $hide = array();
        $report = array();
        try {
            //update report
            $hide = json_decode(DB::table('auto_report_keywords')->where('type', 'hide')->first()->value);

            $report = json_decode(DB::table('auto_report_keywords')->where('type', 'report')->first()->value);
        } catch (\Illuminate\Database\QueryException $e) {
            Log::emergency($e);
            abort(500, "Ops");
        }
        DB::commit();

        return response()->json(['status' => 'success', 'hide' => $hide, 'report' => $report]);
    }

    public function toggleComments(AdminRequest $req, ArticleRepository $repo, $id)
    {
        $status = $req->input('status');
        if ($status == 0) {
            DB::table('disabled_comments')->where('unique_ID', $id)->update([
                'deleted_at' => time()
            ]);
        } else {
            DB::table('disabled_comments')->updateOrInsert(
                ['unique_ID' => $id],
                ['created_at' => time(), 'deleted_at' => null]
            );
        }
        $response = null;
        if (like_match("V%_%", $id)) {
            $vrepo = app(VideoRepository::class);
            $response = $vrepo->getVideo($id);
        } else if (like_match("P%_%", $id)) {
            $prepo = app(PodcastRepository::class);
            $response = $prepo->getPodcast($id);
        } else {
            $response = $repo->getArticle($id);
        }

        //clear cache for this article && disable_comment config
        Cache::forget("a_" . $id . "_2");
        Cache::forget("disabled_comments_" . $id);

        return response()->json($response);
    }

    public function publisherList()
    {
        $data =  DB::table('publishers')->select('id', 'name')->orderBy('name')->get();
        $data = $data->prepend(['id' => -1, 'name' => "All Publishers"]);
        return $data;
    }

    public function getAllEstimateRevenue(AdminRequest $req)
    {
        $startDate = $req->getStartDate();
        $endDate = $req->getEndDate();
        $publisherIds = $req->getPublisherID();

        $service = app(RevenueService::class);

        $summary =  $service->getAdminEstimateSummary($startDate, $endDate, $publisherIds);
        $data =  $service->getAdminAllEstimate($startDate, $endDate, $publisherIds);
        $dataArray = $data->getData(true);
        return response()->json([
            'summary' => $summary,
            'data' => $dataArray['data']
        ]);
    }

    public function getPaginateEstimateRevenue(AdminRequest $req)
    {
        $startDate = $req->getStartDate();
        $endDate = $req->getEndDate();
        $publisherIds = $req->getPublisherID();
        $sortBy = ($req->has('sortBy') && $req->get('sortBy')) ? $req->get('sortBy') : null;
        $sortDirection = $req->has('sortDirection') && $req->get('sortDirection') === 'true' ? 'asc' : 'desc';

        $service = app(RevenueService::class);

        $summary =  $service->getAdminEstimateSummary($startDate, $endDate, $publisherIds);
        $data =  $service->getAdminPaginateEstimate($startDate, $endDate, $publisherIds, $sortBy, $sortDirection);
        $dataArray = $data->getData(true);
        return response()->json([
            'summary' => $summary,
            'data' => $dataArray['data'],
            'pagination' => $dataArray['pagination']
        ]);
    }

    function getCovidData($country = 'Malaysia', $date)
    {
        if (!isset($date)) {
            $date = Carbon::now()->subDays(1);
        }
        $date = $date->format('Y-m-d');

        $data = DB::table('corona_stats_2')
            ->where('country', $country)
            ->where('stats_date', $date)
            ->first();

        return $data;
    }

    private function getUploadedCovidImagesAtributes($img1 = null, $img2 = null, $img3 = null, $img4 = null)
    {
        $img1Link = !$img1 || empty($img1) ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img1);
        $img2Link = !$img2 || empty($img2)  ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img2);
        $img3Link = !$img3 || empty($img3)  ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img3);
        $img4Link = !$img4 || empty($img4) ? null : uploadToCloudFromUrl("corona/imgs/corona-img-" . time() . "-" . rand(1, 999) . ".png", $img4);

        $aImage = [];

        if ($img1Link) {
            $aImage[] = 1;
        }
        if ($img2Link) {
            $aImage[] = 2;
        }
        if ($img3Link) {
            $aImage[] = 3;
        }
        if ($img4Link) {
            $aImage[] = 4;
        }

        $htmlGallery = '';
        $headerImg = null;

        foreach ($aImage as $index => $j) {
            $i = $index + 1;
            $temp = ${"img$j" . "Link"};

            // last image will be the header image
            if ($index == count($aImage) - 1) {
                $headerImg = ${"img$j"} ? "<figure><img  " . 'onClick="' . "showImageDetail('0')" . '" ' . "src=\"$temp\" class=\"fullWidthImg\"/></figure>" : "<p></p>";
            }

            //set gallery
            $htmlGallery .= ${"img$j"} ? "<p><img  " . 'onClick="' . "showImageDetail('$i')" . '" ' . "src=\"$temp\"/></p>" : "<p></p>";
        }

        $mediaIds = [];
        /** @var CreatesMedia $createsMedia */
        $createsMedia = app(CreatesMedia::class);

        if ($img1Link) {
            $id = $createsMedia->execute($img1Link);
            $mediaIds[] = $id;
        }

        if ($img2Link) {
            $id = $createsMedia->execute($img2Link);
            $mediaIds[] = $id;
        }

        if ($img3Link) {
            $id = $createsMedia->execute($img3Link);
            $mediaIds[] = $id;
        }

        if ($img4Link) {
            $id = $createsMedia->execute($img4Link);
            $mediaIds[] = $id;
        }

        if ($headerImg) {
            array_unshift($mediaIds, end($mediaIds));
        }

        // hardcoded media ID as default / postfix photo in gallery
        $mediaIds[] = 6985713;

        $mediaIds = implode(",", $mediaIds);

        $images = new \stdClass();
        $images->headerImg = $headerImg;
        $images->mediaIds = $mediaIds;
        $images->htmlGallery = $htmlGallery;

        return $images;
    }

    public function updateCovidArticleImages(AdminRequest $req)
    {
        $dryMode = app()->env != "production";
        $img1 = $req->input('img1');
        $img2 = $req->input('img2');
        $img3 = $req->input('img3');
        $img4 = $req->input('img4');

        $uploadedImages = $this->getUploadedCovidImagesAtributes($img1, $img2, $img3, $img4);

        $headerImg = $uploadedImages->headerImg;
        $mediaIds = $uploadedImages->mediaIds;
        $htmlGallery = $uploadedImages->htmlGallery;

        $articles = ['covid19__en', 'covid19__zh', 'covid19__ms'];
        $html = null;
        try {
            foreach ($articles as $article) {
                $html = DB::table('article')->where('uniqueID', $article)->first()->html ?? null;
                $html = HtmlDomParser::str_get_html($html);
                $html->find('div[id=header-image]', 0)->innertext = $headerImg;
                $html->find('div[id=html-gallery]', 0)->innertext = $htmlGallery;

                if (!$dryMode) {
                    DB::table('article')->where('uniqueID', $article)->update([
                        'html' => $html,
                        'media' => $mediaIds
                    ]);
                }
                Cache::forget("a_" . $article . "2");

                $updatedData = [
                    'img1' =>  $img1 ?? '',
                    'img2' =>  $img2 ?? '',
                    'img3' =>  $img3 ?? '',
                    'img4' =>  $img4 ?? ''
                ];
                $this->updateCovidArticleHistory('image', $updatedData);
            }
        } catch (\Exception $e) {
            Log::info($e);
            return response()->json(['msg' => 'Something went wrong while replacing images in article'], 400);
        }

        return response($html);
    }

    private function updateCovidArticleHistory($type = 'article', $data): void
    {
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        DB::table('article_history')->updateOrInsert(
            ['article_id' => 'covid_' . $type],
            ['json' => $data]
        );
    }

    public function getCovidArticleHistory(Request $request)
    {
        $type = $request->input('type') ?? 'article';
        $json = DB::table('article_history')->where('article_id', 'covid_' . $type)->first()->json ?? null;
        if (!$json) {
            return response()->json(['message' => 'No data exists'], 400);
        }
        $json = json_decode($json);

        return response()->json($json, 200);
    }


    public function getReportedContents(AdminRequest $req, PostRepository $postrepo)
    {

        $startDate = $req->input('startDate');
        $endDate = $req->input('endDate');
        $id = $req->input('id');
        $title = $req->input('title');
        $sortBy = $req->input('sortBy');
        $sortDirection = $req->has('sortDirection') && $req->get('sortDirection') === 'true' ? 'asc' : 'desc';
        return $postrepo->getReportedContents($startDate, $endDate, $id, $title, $sortBy, $sortDirection);
    }

    public function reportedContentAction(AdminRequest $req, PostRepository $postrepo){
        $id = $req->input('entity_id');
        $entity_type = $req->input('entity_type');
        $review_status = $req->input('review_status');
        $reviewed_by = $req->input('reviewed_by');
        return $postrepo->reportedContentAction($id, $entity_type,$review_status,$reviewed_by);
    }

    public function getAnnouncements(AdminRequest $request, AnnouncementRepository $repo){
        return response()->json($repo->getAnnouncementsAdmin($request));
    }

    public function getOneAnnouncement(AnnouncementRepository $repo, $id = null){
        return response()->json($repo->getOneAnnouncementAdmin($id));
    }

    public function createAnnouncement(AnnouncementRequest $request, AnnouncementRepository $repo){
        return $repo->createAnnouncement($request);
    }

    public function updateAnnouncement(AnnouncementRequest $request, $id, AnnouncementRepository $repo){
        return $repo->updateAnnouncement($request, $id);
    }

    public function deleteAnnouncement($id, AnnouncementRepository $repo){
        return $repo->deleteAnnouncement($id);
    }


    private function getAdminUser(){
        return NewswavAuth::getUser();
    }

    public function getHighlights(Request $req, HighlightsRepository $repo, $id = null){
        $limit = $req->get('limit');
        $highlights = $repo->getHighlightsAdmin($id, $limit);
        $highlights = $highlights->toArray();
        $highlightsData = $highlights['data'];
        unset($highlights['data']);

        return response()->json([
            'highlights' => $highlightsData,
            'pagination' => $highlights
        ]);
    }

    public function getPublisherHighlights(Request $req, PublisherRepository $repo){
        $name = $req->get('name');
        return response()->json($repo->highlightPublisherList($name));
    }

    public function createHighlight(HighlightsRequest $req, HighlightsRepository $repo){
        return $repo->createHighlight($req);
    }

    public function updateHighlight(HighlightsRequest $req, HighlightsRepository $repo, $id){
        return $repo->updateHighlight($req, $id);
    }

    public function deleteHighlight(AdminRequest $req, HighlightsRepository $repo, $id){
        return $repo->deleteHighlight($req, $id);
    }

    public function getHighlightContents(Request $request,HighlightsRepository $repo, $id){
        $search = $request->get('search');
        $type = $request->get('type');
        return $repo->getHighlightContentsAdmin($id, $search, $type);
    }

    public function hideHighlightContent(HighlightsRepository $repo, $id, $uniqueID){
        return $repo->disableHighlightContent($id, $uniqueID);
    }

    public function unhideHighlightContent(HighlightsRepository $repo, $id, $uniqueID){
        return $repo->enableHighlightContent($id, $uniqueID);
    }

    public function clearCacheGroup($value){
        cache_group_remove($value);
        return response()->json(["message" => "done"]);
    }

    private function getActiveElection(){
        $activeElection = DB::connection('election_cluster')->table('e_active_election')->first();
        if (!$activeElection){
            response()->json([
                'message' => 'No Active Election'
            ], 404)->throwResponse();
        }
        $activeElectionCode = $activeElection->code;
        $election = DB::connection('election_cluster')->table('e_elections')->where('code', $activeElectionCode)->first();
        if (!$election){
            response()->json([
                'message' => 'Election Not Found'
            ], 404)->throwResponse();
        }
        return $election;
    }

    public function getElectionStates(Request $request){
        $election = $this->getActiveElection();
        $query = DB::connection('election_cluster')->table('e_states');
        if ($election->is_general == 0){
            $query->where('id', $election->state_id)->where('is_federal', 0);
        }
        $states = $query->get();
        return response()->json([
            "general" => $election->is_general ? true : false,
            "states" => $states
        ]);
    }

    public function getElectionCandidates(Request $request){
        $election = $this->getActiveElection();
        $type = $request->get('type');
        $areaId = $request->get('areaId');
        $query = DB::connection('election_cluster')->table('e_candidates as c')
                    ->select('c.id', 'c.name', 'p.abbreviation', 'p.color', 'p.logo as image', 'co.abbreviation as coalition')
                    ->join('e_parties as p', 'p.id', '=', 'c.party_id')
                    ->join('e_coalitions as co', 'co.id', '=', 'p.coalition_id')
                    ->where('election_id', $election->id);

        if($type && $areaId){
            if ($type == "parliament"){
                $query->where('parliament_id', $areaId);
            }
            if ($type == "state"){
                $query->where('area_id', $areaId);
            }
        }
        $candidates = $query->get()->map(function ($item) {
            $item->search = $item->abbreviation . " " . $item->name;
            $item->image = $item->image ? (!Str::contains($item->image, 'http') ? nw_bunker('host', 'admin_cdn', 'https://cdn.newswav.com/admin2/') . $item->image : $item->image) : null;
            return $item;
        });
        return $candidates;
    }


    public function getElectionData(Request $request, $stateId){
        $type = $request->get('type');
        DB::beginTransaction();
        try{
            $election = $this->getActiveElection();
            if (!$election){
                abort(404, "not found");
            }
            $stateNoEdit = [];
            try {
                if (!$election->meta){
                    throw new Exception("election meta not found");
                }
                $meta = json_decode($election->meta);
                $service = new $meta->service_class;
                $meta = $service->getElectionMeta();
                $stateNoEdit = $meta->meta->state_no_edit;
            } catch (\Exception $e){
                notify_now($e);
            }
            $noEdit = false;
            if ($type == "state"){
                $noEdit = (DB::connection('election_cluster')->table('e_states')->whereIn('code', $stateNoEdit)->where('id', $stateId)->first()) ? true : false;
                $areas = DB::connection('election_cluster')->table('e_state_areas')
                            ->where('state_id', $stateId)->get();
                $areasResults = DB::connection('election_cluster')->table('e_area_results')
                                    ->whereIn('area_id', $areas->pluck('id')->toArray())
                                    ->where('election_id', $election->id)->get()->keyBy('area_id');

                $candidates = DB::connection('election_cluster')->table('e_candidates as c')
                                ->select('c.id', 'c.name', 'p.abbreviation', 'p.color', 'p.logo as image', 'co.abbreviation as coalition')
                                ->join('e_parties as p', 'p.id', '=', 'c.party_id')
                                ->join('e_coalitions as co', 'co.id', '=', 'p.coalition_id')
                                ->whereIn('area_id', $areas->pluck('id')->toArray())
                                ->where('election_id', $election->id)->get()->map(function ($item) {
                                    $item->search = $item->abbreviation . " " . $item->name;
                                    $item->image = $item->image ? (!Str::contains($item->image, 'http') ? nw_bunker('host', 'admin_cdn', 'https://cdn.newswav.com/admin2/') . $item->image : $item->image) : null;
                                    return $item;
                                })->keyBy('id');

                $state = $areas->map(function($area) use($areasResults, $candidates, $noEdit){
                    $result = $areasResults->get($area->id) ?? null;
                    $area->candidate = $result ? $candidates->get($result->candidate_id) ?? null : null;
                    $area->original_candidate = $area->candidate ?? null;
                    $area->official = $result ? ($result->official == true ? true : false) : false;
                    $area->original_official = $area->official ?? null;
                    $area->canEdit = !$noEdit;
                    return $area;
                });
            }

            if ($type == "parliament"){
                $parliaments = DB::connection('election_cluster')->table('e_parliament_areas')->where('state_id', $stateId)->get();
                $parliamentsResults = DB::connection('election_cluster')->table('e_parliament_results')
                                    ->whereIn('parliament_id', $parliaments->pluck('id')->toArray())
                                    ->where('election_id', $election->id)->get()->keyBy('parliament_id');

                $candidates = DB::connection('election_cluster')->table('e_candidates as c')
                                ->select('c.id', 'c.name', 'p.abbreviation', 'p.color', 'p.logo as image', 'co.abbreviation as coalition')
                                ->join('e_parties as p', 'p.id', '=', 'c.party_id')
                                ->join('e_coalitions as co', 'co.id', '=', 'p.coalition_id')
                                ->whereIn('parliament_id', $parliaments->pluck('id')->toArray())
                                ->where('election_id', $election->id)->get()->map(function ($item) {
                                    $item->search = $item->abbreviation . " " . $item->name;
                                    $item->image = $item->image ? (!Str::contains($item->image, 'http') ? nw_bunker('host', 'admin_cdn', 'https://cdn.newswav.com/admin2/') . $item->image : $item->image) : null;
                                    return $item;
                                })->keyBy('id');

                $state = $parliaments->map(function($parliament) use($parliamentsResults, $candidates, $noEdit){
                    $result = $parliamentsResults->get($parliament->id) ?? null;
                    $parliament->candidate = $result ? $candidates->get($result->candidate_id) ?? null : null;
                    $parliament->original_candidate = $parliament->candidate ?? null;
                    $parliament->official = $result ? ($result->official == true ? true : false) : false;
                    $parliament->original_official = $parliament->official ?? null;
                    $parliament->canEdit = !$noEdit;
                    return $parliament;
                });
            }

            DB::commit();

            return response()->json($state);
        }catch(Exception $e){
            DB::rollBack();
            notify_now($e);
        }

    }

    public function updateElectionData(Request $request){
        $election = $this->getActiveElection();
        $type = $request->get('type');
        $areaId = $request->get('areaId');
        $electionId = $election->id;
        $candidateId = $request->get('candidateId');
        $official = $request->get('official');

        if ($type == "parliament"){
            $table = "e_parliament_results";
            $column = "parliament_id";
        }

        if ($type == "state"){
            $table = "e_area_results";
            $column = "area_id";
        }

        DB::beginTransaction();
        try{
            DB::connection('election_cluster')->table($table)->updateOrInsert(
                ['election_id' => $electionId, $column => $areaId],
                ['candidate_id' => $candidateId, 'official' => $official]
            );
            DB::connection('election_cluster')->table('e_elections')
                ->where('id', $electionId)
                ->update(['updated_at' => now()->toDateTimeString()]);

            // call election service api to recreate redis cache
            /** @var ElectionServiceClient  $electionServiceClient */
            $electionServiceClient = app(ElectionServiceClient::class);
            $electionServiceClient->recreateCache($electionId);

            // emit updated websocket
            /** @var ElectionBroadcastService $broadcastService */
            $broadcastService = app(ElectionBroadcastService::class);
            $broadcastService->broadcastUpdate($election->code);

            DB::commit();
            return response()->json([
                "message" => "YEAAAHHHHHH"
            ]);
        }catch(Exception $e){
            DB::rollBack();
            notify_now($e);
            return response()->json([
                "message" => $e->getMessage()
            ], 400);
        }
    }

    public function publisherPriorityList(PublisherRepository $repo){
        return response()->json(['priority_list' => $repo->publisherPriorityList()]);
    }

    public function addToPublisherPriorityList(PublisherRepository $repo, Request $req){
        $ids = $req->input('pubId');
        return $repo->addToPNPriorityPubList($ids);
    }

    public function removeFromPublisherPriority(PublisherRepository $repo, $id){
        return $repo->removeFromPNPriorityPubList($id);
    }

    public function getPublisherMonitoringSetting(MonitoringService $service, $publisherId){
        return response()->json($service->getPublisherSetting((int)$publisherId));
    }

    public function updatePublisherMonitoringSetting(MonitoringRequest $request, MonitoringService $service, $publisherId){
        $status = $request->getStatus();
        $frequency = $request->getFrequency();
        $authToken = $request->bearerToken();
        return response()->json($service->updatePublisherSetting((int)$publisherId, $status, $frequency, $authToken));
    }

    public function rebuildElectionSearchData(){

        try {
            $election = $this->getActiveElection();
            if (!$election->meta){
                throw new Exception("election meta not found");
            }
            $meta = json_decode($election->meta);
            $service = new $meta->service_class;
            return $service->rebuildSearchCache();
        } catch (\Exception $e){
            abort(400, $e->getMessage());
        }


    }

    public function translateArticles(Request $request, string $uniqueId, TranslateArticlesLogic $translateArticlesLogic) : array {
        return $translateArticlesLogic->execute($uniqueId, $request->language);
    }

    public function getAiPnConfigurations(GetAiPnConfigurationLogic $getAiPnConfigurationsLogic) : JsonResponse {
        return $getAiPnConfigurationsLogic->execute();
    }

    public function createAiPnConfiguration(AiPnConfigurationsRequestV4 $request, CreateAiPnConfigurationLogic $createAiPnConfigurationsLogic): JsonResponse {
        return  $createAiPnConfigurationsLogic->execute($request);
    }

    public function updateAiPnConfiguration(AiPnConfigurationsRequestV4 $request, UpdateAiPnConfigurationLogic $updateAiPnConfigurationsLogic, int $id): JsonResponse {
        return $updateAiPnConfigurationsLogic->execute($request, $id);
    }

    public function getAiPnConfigurationById(GetAiPnConfigurationLogic $getAiPnConfigurationsLogic, int $id): JsonResponse {
        return $getAiPnConfigurationsLogic->execute($id);
    }

    public function deleteAiPnConfiguration(int $id, DeleteAiPnConfigurationLogic $deleteAiPnConfigurationsLogic): JsonResponse {
        return  $deleteAiPnConfigurationsLogic->execute($id);
    }

    public function getPnTitleAndDescriptionSuggestion(GetPNTitleAndDescriptionSuggestionLogic $getPNTitleAndDescriptionSuggestionLogic, string $uniqueId): array {
        return $getPNTitleAndDescriptionSuggestionLogic->execute($uniqueId);
    }

    public function getSlackChannels(RetrieveSlackChannelsLogic $RetrieveSlackChannelsLogic) : JsonResponse {
        return $RetrieveSlackChannelsLogic->execute();
    }

    public function getMostViewedArticles(MostViewedArticlesRequest $mostViewedArticlesRequest, RetrieveMostViewedArticlesLogic $RetrieveMostViewedArticlesLogic) : JsonResponse{
        $fromDate = $mostViewedArticlesRequest->getFromDate();
        $toDate = $mostViewedArticlesRequest->getToDate();
        $languages = $mostViewedArticlesRequest->getLanguage();
        $languageArray = $languages ? explode(',', $languages) : null;
        $uniqueId = $mostViewedArticlesRequest->getUniqueId();
        $projects = $mostViewedArticlesRequest->getProjects();
        $projectArray = $projects ? explode(',', $projects) : null;
        $page = $mostViewedArticlesRequest->getPage();

        return $RetrieveMostViewedArticlesLogic->execute($fromDate, $toDate, $languageArray, $uniqueId, $projectArray, $page);
    }
}
