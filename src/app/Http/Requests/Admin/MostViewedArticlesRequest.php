<?php
namespace App\Http\Requests\Admin;

class MostViewedArticlesRequest extends AdminRequest
{

    public function rules()
    {
        return [
            'fromDate' => 'required',
            'toDate' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'fromDate.required' => 'fromDate is required!',
            'toDate.required' => 'toDate is required!',
        ];
    }

    public function getFromDate(): string
    {
        return $this->input('fromDate');
    }

    public function getToDate(): string
    {
        return $this->input('toDate');
    }

    public function getLanguage(): ?string
    {
        return $this->input('language');
    }

    public function getUniqueId(): ?string
    {
        return $this->input('uniqueId');
    }

    public function getPage(): int
    {
        return $this->input('page') ?? 1;
    }

    public function getProjects(): ?string
    {
        return $this->input('projects');
    }
}
