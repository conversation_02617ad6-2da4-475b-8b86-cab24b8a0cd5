{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "require": {"php": "^7.4", "bugsnag/bugsnag-laravel": "^2.0", "doctrine/dbal": "^2.9", "drewm/mailchimp-api": "^2.5", "elasticsearch/elasticsearch": "^7.0", "firebase/php-jwt": "^5.0", "google/cloud-pubsub": "^1.46", "google/cloud-storage": "^1.10", "illuminate/mail": "^8.0", "illuminate/redis": "^8.0", "jenssegers/agent": "^2.6", "kevinrob/guzzle-cache-middleware": "^1.4.0", "kreait/firebase-php": "^5.11", "kubernetes/php-client": "^1.14", "laravel/lumen-framework": "^8.0", "launchdarkly/server-sdk": "^4.0", "launchdarkly/server-sdk-redis-predis": "^1.1", "lcobucci/jwt": "3.3.1", "mongodb/laravel-mongodb": "^3.0.0", "monolog/monolog": "^2.0", "newswav/laravel-central-schema": "^0.0.53", "newswav/newswav-auth": "^1.0.15", "predis/predis": "1.1.1", "pusher/pusher-php-server": "^5.0", "spatie/array-to-xml": "^2.15", "swooletw/laravel-swoole": "^2.6.69", "symfony/yaml": "^5.0", "vlucas/phpdotenv": "^5.0", "voku/simple_html_dom": "^4.7", "webpatser/laravel-uuid": "^3.0"}, "require-dev": {"fzaninotto/faker": "~1.4", "mockery/mockery": "~1.0", "phpunit/phpunit": "~7.0", "rector/rector": "^1.1"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/", "Database\\Factories\\V4\\Models\\": "database/factories/V4/", "Spatie\\Newsletter\\": "newsletter/"}, "files": ["app/Helpers/error_functions.php", "app/Helpers/int_to_string_object.php", "app/Helpers/jwt.php", "app/Helpers/cache.php", "app/Helpers/cache_feed.php", "app/Helpers/article_tools.php", "app/Helpers/download_tools.php", "app/Helpers/tools.php", "app/Helpers/scalers.php", "app/Helpers/notify_crawler.php", "bunker/bunker.php", "app/Helpers/common.php", "app/Helpers/color_tools.php", "app/Helpers/boost_stats.php"]}, "autoload-dev": {"classmap": ["tests/TestCase.php"], "psr-4": {"Tests\\": "tests/", "App\\": "app/"}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://github.com/Newswav/Newswav-Auth-<PERSON>"}, {"type": "vcs", "url": "https://github.com/Newswav/laravel-central-schema"}]}