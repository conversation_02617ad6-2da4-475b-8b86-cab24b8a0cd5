<?php

namespace Tests;

use Faker\Factory;
use Tests\Traits\TestTraits;

abstract class TestCase extends \Laravel\Lumen\Testing\TestCase
{
    use TestTraits;

    /** @var Factory */
    public $generator;

    public function setUp(): void {
        parent::setUp();
        $this->generator = Factory::create();
    }

    public function createApplication()
    {
        return require __DIR__.'/../bootstrap/app.php';
    }
}
