<?php

namespace Tests\Unit\Modules\Media\Services;

use App\Modules\Media\Services\CreatesMedia;
use App\Repositories\Media\MediaRepository;
use Faker\Factory;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class CreatesMediaTest extends TestCase
{
    public function testItCreatesMedia(): void {
        $url = app()->basePath('public/img/working.jpg');
        $expectedWidth = 1200;
        $expectedHeight = 675;
        $expectedCaption = $this->generator->title;
        $expectedMediaId = 1;

        /** @var MediaRepository|MockInterface */
        $mediaRepositoryMock = Mockery::mock(MediaRepository::class);
        $mediaRepositoryMock->shouldReceive('addMedia')->once()->with(
            $url, $expectedCaption, $expectedWidth, $expectedHeight
        )->andReturn($expectedMediaId);

        $service = new CreatesMedia($mediaRepositoryMock);
        $mediaId = $service->execute($url, $expectedCaption);

        static::assertEquals($expectedMediaId, $mediaId);
    }

    public function testInvalidImageUrl(): void {
        $generator = Factory::create();

        $url = app()->basePath('public/img/appstores.png');
        $expectedCaption = $generator->title;
        $expectedMediaId = 1;

        /** @var MediaRepository|MockInterface */
        $mediaRepositoryMock = Mockery::mock(MediaRepository::class);
        $mediaRepositoryMock->shouldReceive('addMedia')->once()->with(
            $url, $expectedCaption, 0, 0
        )->andReturn($expectedMediaId);

        $service = new CreatesMedia($mediaRepositoryMock);
        $mediaId = $service->execute($url, $expectedCaption);

        static::assertEquals($expectedMediaId, $mediaId);
    }

    public function testPartiallyCorruptedImageUrl(): void {
        $generator = Factory::create();

        $url = app()->basePath('public/img/corrupt.jpg');
        $expectedCaption = $generator->title;
        $expectedMediaId = 1;

        /** @var MediaRepository|MockInterface */
        $mediaRepositoryMock = Mockery::mock(MediaRepository::class);
        $mediaRepositoryMock->shouldReceive('addMedia')->once()->with(
            $url, $expectedCaption, 0, 0
        )->andReturn($expectedMediaId);

        $service = new CreatesMedia($mediaRepositoryMock);
        $mediaId = $service->execute($url, $expectedCaption);

        static::assertEquals($expectedMediaId, $mediaId);
    }
}
