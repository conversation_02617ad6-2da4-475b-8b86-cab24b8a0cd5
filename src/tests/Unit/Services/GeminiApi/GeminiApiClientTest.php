<?php

namespace Tests\Unit\Services\GeminiApi;

use App\Exceptions\NwApiException;
use App\Services\GeminiApi\AccessTokenProvider;
use App\Services\GeminiApi\GeminiApiClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Response;
use Psr\Http\Message\RequestInterface;
use Tests\TestCase;
use Mockery;

class GeminiApiClientTest extends TestCase
{
    public function testSuccessfulGetResponse()
    {
        $fakeText = $this->generator->text;

        $expectedResponse = [
            'candidates' => [
                'content' => [
                    'role' => 'model',
                    'parts' => [
                        'text' => $fakeText,
                    ]
                ],
                'finishReasons' => 'STOP'
            ]
        ];

        $fakeResponse = new Response(200, [], json_encode($expectedResponse));

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('post')
            ->once()
            ->andReturn($fakeResponse);

        $accessTokenProviderMock = Mockery::mock(AccessTokenProvider::class);
        $accessTokenProviderMock->shouldReceive('execute')
        ->once()
        ->andReturn($fakeText);

        $service = new GeminiApiClient($clientMock, $accessTokenProviderMock);

        $this->assertEquals($expectedResponse, $service->getResponse($fakeText, $fakeText));
    }

    public function testThrowNwApiException()
    {
        $this->expectException(NwApiException::class);

        $fakeText = $this->generator->text;

        $mockRequest = Mockery::mock(RequestInterface::class);
        $requestException = new RequestException('Error', $mockRequest);

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('post')
            ->once()
            ->andThrow($requestException);

        $accessTokenProviderMock = Mockery::mock(AccessTokenProvider::class);
        $accessTokenProviderMock->shouldReceive('execute')
        ->once()
        ->andReturn($fakeText);

        $service = new GeminiApiClient($clientMock, $accessTokenProviderMock);

        $service->getResponse($fakeText,$fakeText);
    }

}
