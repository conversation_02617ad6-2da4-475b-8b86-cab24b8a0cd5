<?php

namespace Tests\Unit\Services\GeminiApi;

use Tests\TestCase;
use App\Services\GeminiApi\AccessTokenProvider;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Google\Auth\ApplicationDefaultCredentials;
use Mockery;

class AccessTokenProviderTest extends TestCase
{
    public function testAccessTokenProviderWithKeyPath() : void
    {
        // use overload to create an 'instance mock', bypassing the actual class's operations such as reading the file path in config()
        $mockCredentials = Mockery::mock('overload:' . ServiceAccountCredentials::class);
        $mockCredentials->shouldReceive('fetchAuthToken')
            ->once()
            ->andReturn(['access_token' => 'fake_access_token']);

        config(['cloud.gemini_api.key_path' => '/fake/path/to/keyfile.json']);

        $provider = new AccessTokenProvider;
        $result = $provider->execute();

        $this->assertEquals('fake_access_token', $result);
    }

    public function testAccessTokenProviderWithoutKeyPath() : void
    {
        // use alias to mock static method 'getCredentials'
        $mockCredentials = Mockery::mock('alias:' . ApplicationDefaultCredentials::class);
        $mockCredentials->shouldReceive('getCredentials')
            ->once()
            ->with(['https://www.googleapis.com/auth/cloud-platform'])
            ->andReturn(Mockery::self());
        $mockCredentials->shouldReceive('fetchAuthToken')
            ->once()
            ->andReturn(['access_token' => 'fake_access_token']);

        config(['cloud.gemini_api.key_path' => null]);

        $provider = new AccessTokenProvider;
        $result = $provider->execute();

        $this->assertEquals('fake_access_token', $result);
    }
}
