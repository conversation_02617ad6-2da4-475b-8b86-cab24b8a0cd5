<?php

namespace Services\GeminiApi\Services;

use App\Exceptions\NwServiceException;
use App\Services\GeminiApi\Constants\GeminiApiModel;
use App\Services\GeminiApi\GeminiApiClient;
use App\Services\GeminiApi\Constants\HarmBlockThreshold;
use App\Services\GeminiApi\Constants\HarmCategory;
use App\Services\GeminiApi\Services\TranslateText;
use App\V4\Common\Constants;
use Tests\TestCase;
use Mockery;

class TranslateTextTest extends TestCase
{
    public function testTranslateHtmlContent() : void
    {
        $fakeText = $this->generator->text;
        $language = Constants::EN;

        $clientMock = Mockery::mock(GeminiApiClient::class);
        $translateHtmlContent = new TranslateText($clientMock);

        $safetySettings = [
            [
                "category" => HarmCategory::HARASSMENT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::HATE_SPEECH,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::SEXUALLY_EXPLICIT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::DANGEROUS_CONTENT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ]
        ];
        $systemInstructions = sprintf(Constants::TRANSLATION_AI_INSTRUCTIONS, $language);
        $expectedResponse = [
            'candidates' => [
                0 => [
                    'content' => [
                        'parts' => [
                            0 => [
                                'text' => $fakeText
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $clientMock->shouldReceive('getResponse')
            ->once()
            ->with($fakeText, $systemInstructions, $safetySettings, GeminiApiModel::GEMINI_FLASH)
            ->andReturn($expectedResponse);

        $translateHtmlContent->execute($fakeText, $language);
    }

    public function testThrowNwServiceException() : void
    {
        $this->expectException(NwServiceException::class);

        $fakeText = $this->generator->text;
        $language = Constants::EN;

        $clientMock = Mockery::mock(GeminiApiClient::class);
        $translateHtmlContent = new TranslateText($clientMock);

        $safetySettings = [
            [
                "category" => HarmCategory::HARASSMENT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::HATE_SPEECH,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::SEXUALLY_EXPLICIT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ],
            [
                "category" => HarmCategory::DANGEROUS_CONTENT,
                "threshold" => HarmBlockThreshold::BLOCK_NONE,
            ]
        ];
        $systemInstructions = sprintf(Constants::TRANSLATION_AI_INSTRUCTIONS, $language);
        $mockBlockedResponse = [
            'promptFeedback' => [
                'blockReason' => 'PROHIBITED_CONTENT'
            ]
        ];

        $clientMock->shouldReceive('getResponse')
            ->once()
            ->with($fakeText, $systemInstructions, $safetySettings, GeminiApiModel::GEMINI_FLASH)
            ->andReturn($mockBlockedResponse);

        $translateHtmlContent->execute($fakeText, $language);
    }
}
