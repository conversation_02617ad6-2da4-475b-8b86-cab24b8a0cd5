<?php

namespace Tests\Unit\V4\Modules\Auth\Logics;

use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Modules\Auth\FirebaseClient;
use App\V4\Modules\Auth\Logics\VerifyEmailLoginLinkLogic;
use Kreait\Firebase\Auth;
use Kreait\Firebase\Auth\SignIn\FailedToSignIn;
use Kreait\Firebase\Auth\SignInResult;
use Tests\TestCase;
use Mockery;

class VerifyEmailLoginLinkLogicTest extends TestCase
{
    public function testItVerifyEmailLoginLink(): void
    {
        $fakeEmail = $this->generator->email;
        $oobCode = $this->generator->uuid;
        $fakeSignInUrl = sprintf('%s?oobCode=%s', $this->generator->url, $oobCode);
        $fakeSignInResult = SignInResult::fromData([]);
        $authMock = Mockery::mock(Auth::class);
        $authMock->shouldReceive('signInWithEmailAndOobCode')
            ->once()
            ->with($fakeEmail, $oobCode)
            ->andReturn($fakeSignInResult);

        $firebaseClient = Mockery::mock(FirebaseClient::class);
        $firebaseClient->shouldReceive('getFirebaseAuth')
            ->andReturn($authMock);

        $service = new VerifyEmailLoginLinkLogic($firebaseClient);
        $service->execute($fakeEmail, $fakeSignInUrl);
    }

    public function testItThrowsNwBadRequestForInvalidSignInLink(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeEmail = $this->generator->email;
        $fakeSignInUrl = $this->generator->url;
        $authMock = Mockery::mock(Auth::class);
        $authMock->shouldNotReceive('signInWithEmailAndOobCode');

        $firebaseClient = Mockery::mock(FirebaseClient::class);
        $firebaseClient->shouldReceive('getFirebaseAuth')
            ->andReturn($authMock);

        $service = new VerifyEmailLoginLinkLogic($firebaseClient);
        $service->execute($fakeEmail, $fakeSignInUrl);
    }

    public function testItThrowsNwBadRequestForSignInLinkExpired(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeEmail = $this->generator->email;
        $oobCode = $this->generator->uuid;
        $fakeSignInUrl = sprintf('%s?oobCode=%s', $this->generator->url, $oobCode);
        $authMock = Mockery::mock(Auth::class);
        $authMock->shouldReceive('signInWithEmailAndOobCode')
            ->once()
            ->with($fakeEmail, $oobCode)
            ->andThrows(FailedToSignIn::class);

        $firebaseClient = Mockery::mock(FirebaseClient::class);
        $firebaseClient->shouldReceive('getFirebaseAuth')
            ->andReturn($authMock);

        $service = new VerifyEmailLoginLinkLogic($firebaseClient);
        $service->execute($fakeEmail, $fakeSignInUrl);
    }
}
