<?php

namespace Tests\Unit\V4\Modules\Auth\Logics;

use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Modules\Auth\FirebaseClient;
use App\V4\Modules\Auth\Logics\SendEmailLoginLinkLogic;
use Illuminate\Support\Facades\Config;
use Kreait\Firebase\Auth;
use Tests\TestCase;
use Mockery;

class SendEmailLoginLinkLogicTest extends TestCase
{
    public function testItSendEmailLoginLink(): void
    {
        $fakeEmail = $this->generator->email;

        $redirectUrl = 'https://newswav.com/email-login';
        Config::shouldReceive('get')
            ->with('firebase.passwordless_signin_redirect_url', '')
            ->once()
            ->andReturn($redirectUrl);
        $authMock = Mockery::mock(Auth::class);
        $authMock->shouldReceive('sendSignInWithEmailLink')
            ->once()
            ->with($fakeEmail, ['url' => $redirectUrl])
            ->andReturnNull();

        $firebaseClient = Mockery::mock(FirebaseClient::class);
        $firebaseClient->shouldReceive('getFirebaseAuth')
            ->andReturn($authMock);

        $service = new SendEmailLoginLinkLogic($firebaseClient);
        $service->execute($fakeEmail);
    }

    public function testItThrowsNwBadRequest(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeEmail = $this->generator->email;

        $redirectUrl = 'https://newswav.com/email-login';
        Config::shouldReceive('get')
            ->with('firebase.passwordless_signin_redirect_url', '')
            ->once()
            ->andReturn($redirectUrl);
        $authMock = Mockery::mock(Auth::class);
        $authMock->shouldReceive('sendSignInWithEmailLink')
            ->once()
            ->with($fakeEmail, ['url' => $redirectUrl])
            ->andThrows(NwBadRequestExceptionV4::class);

        $firebaseClient = Mockery::mock(FirebaseClient::class);
        $firebaseClient->shouldReceive('getFirebaseAuth')
            ->andReturn($authMock);

        $service = new SendEmailLoginLinkLogic($firebaseClient);
        $service->execute($fakeEmail);
    }
}
