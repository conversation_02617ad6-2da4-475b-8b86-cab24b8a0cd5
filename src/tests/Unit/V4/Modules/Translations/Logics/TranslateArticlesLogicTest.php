<?php

namespace Tests\Unit\V4\Modules\Translations\Logics;

use App\Services\Cache\CacheKeyService;
use App\Services\GeminiApi\Services\TranslateText;
use App\V4\Models\ArticleContent;
use App\V4\Modules\Translations\Logics\TranslateArticlesLogic;
use App\Services\GeminiApi\Services\TranslateHtmlContent;
use App\Exceptions\NwServiceException;
use App\V4\Common\Constants;
use App\V4\Exceptions\ArticleNotFoundException;
use App\V4\Exceptions\NwForbiddenExceptionV4;
use App\V4\Models\Article;
use App\V4\Models\Channel;
use App\V4\Repositories\ArticleRepository;
use App\V4\ValueObjects\ArticleTranslationObject;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Mockery;

class TranslateArticlesLogicTest extends TestCase
{
    /*
        Default behavior of class, when target language is not specified.
        Returns 2 translations.
    */
    public function testTranslateArticlesLogic() : void
    {
        $fakeArticle = Article::factory()->make();
        $fakeChannel = Channel::factory()->make();
        $fakeArticle->setRelation('channel', $fakeChannel);
        $fakeArticle->channel->reader_view_only = true;
        $fakeArticle->channel->language = Constants::EN;
        $languageMap = Constants::LANGUAGE_MAP;

        $fakeTranslatedText = $this->generator->text;
        $fakeTranslatedTitle = $this->generator->title;
        $translatedContents = [
            new ArticleTranslationObject(
                Constants::ZH,
                $fakeTranslatedText,
                $fakeTranslatedTitle
            ),
            new ArticleTranslationObject(
                Constants::MS,
                $fakeTranslatedText,
                $fakeTranslatedTitle
            )
        ];

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findBy')
            ->once()
            ->with('uniqueId', $fakeArticle->uniqueID)
            ->andReturn($fakeArticle);

        $translateHtmlContentMock = Mockery::mock(TranslateHtmlContent::class);
        $translateTextMock = Mockery::mock(TranslateText::class);
        $articleContentMock = Mockery::mock(ArticleContent::class);
        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);


        // Get notice language based on target languages
        $getExpectedNoticeMessage = function($targetLanguage) use ($languageMap, $fakeArticle) {
            $dynamicLanguage = $languageMap[$targetLanguage][$fakeArticle->channel->language];
            $notices = Constants::TRANSLATED_ARTICLE_LABELS;

            return sprintf(
                Constants::TRANSLATION_NOTICE_HTML_TAG,
                sprintf($notices[$targetLanguage], $dynamicLanguage)
            );
        };

        foreach ($translatedContents as $translatedContent) {
            $translateHtmlContentMock->shouldReceive('execute')
                ->with(
                    $fakeArticle->html,
                    [Constants::EN, $translatedContent->getLanguage()]
                )->andReturn($translatedContent->getTranslatedContent());

            $translateTextMock->shouldReceive('execute')
                ->with(
                    $fakeArticle->title,
                    $languageMap[Constants::EN][$translatedContent->getLanguage()]
                )->andReturn($translatedContent->getTranslatedTitle());

            $articleContentMock->shouldReceive('updateOrCreate')
                ->with(
                    ['unique_id' => $fakeArticle->uniqueID, 'language' => $translatedContent->getLanguage()],
                    [
                        'html' => $getExpectedNoticeMessage($translatedContent->getLanguage()) . $translatedContent->getTranslatedContent(),
                        'title' => $translatedContent->getTranslatedTitle(),
                    ]
                );

            $legacyCacheKey = sprintf("legacy-article-content-%s", $fakeArticle->uniqueID);
            $cacheKeyServiceMock->shouldReceive('getLegacyArticleContentCacheKey')
                ->with($fakeArticle->uniqueID)
                ->andReturn($legacyCacheKey);
            $legacyArticleContentCache = Mockery::mock();
            $legacyArticleContentCache->shouldReceive('remove')
                ->with($legacyCacheKey)
                ->andReturn(true);;

            $cacheKey = sprintf("article-html-%s-%s", $fakeArticle->uniqueID, $translatedContent->getLanguage());
            $cacheKeyServiceMock->shouldReceive('getArticleHtmlCacheKey')
                ->with($fakeArticle->uniqueID, $translatedContent->getLanguage())
                ->andReturn($cacheKey);

            $articleHtmlCache = Mockery::mock();
            $articleHtmlCache->shouldReceive('put')
                ->with($cacheKey, $translatedContent);
            Cache::shouldReceive('store')
                ->with('redis')
                ->andReturn($articleHtmlCache);
        }

        $translateArticlesLogic = new TranslateArticlesLogic(
            $translateHtmlContentMock,
            $articleRepositoryMock,
            $translateTextMock,
            $articleContentMock,
            $cacheKeyServiceMock
        );

        $translatedData = $translateArticlesLogic->execute($fakeArticle->uniqueID);
        $currentArticleLanguage = $fakeArticle->channel->language;

        // Filter current article lang to get target languages
        $targetLanguages = array_values(
            array_filter(array_keys($languageMap), function($lang) use ($currentArticleLanguage) {
                return $lang !== $currentArticleLanguage;
            })
        );

        // Check for each target language
        $this->assertCount(2, $translatedData);
        foreach ($targetLanguages as $index => $targetLanguage) {
            $this->assertSame($targetLanguage, $translatedData[$index]->getLanguage());
            $this->assertSame(
                $getExpectedNoticeMessage($targetLanguage) . $fakeTranslatedText,
                $translatedData[$index]->getTranslatedContent()
            );
            $this->assertSame(
                $fakeTranslatedTitle,
                $translatedData[$index]->getTranslatedTitle()
            );
        }
    }

    public function testThrowArticleNotFoundException() : void
    {
        $this->expectException(ArticleNotFoundException::class);

        $fakeArticle = Article::factory()->make();

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findBy')
            ->once()
            ->with('uniqueId', $fakeArticle->uniqueID)
            ->andReturnNull();

        $translateHtmlContentMock = Mockery::mock(TranslateHtmlContent::class);
        $translateHtmlContentMock->shouldNotReceive('execute');

        $translateTextMock = Mockery::mock(TranslateText::class);
        $translateTextMock->shouldNotReceive('execute');

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldNotReceive('updateOrCreate');

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldNotReceive('getArticleHtmlCacheKey');


        $translateArticlesLogic = new TranslateArticlesLogic(
            $translateHtmlContentMock,
            $articleRepositoryMock,
            $translateTextMock,
            $articleContentMock,
            $cacheKeyServiceMock
        );

        $translateArticlesLogic->execute($fakeArticle->uniqueID);
    }

    public function testThrowNwForbiddenExceptionV4() : void
    {
        $this->expectException(NwForbiddenExceptionV4::class);

        $fakeArticle = Article::factory()->make();
        $fakeChannel = Channel::factory()->make();
        $fakeArticle->setRelation('channel', $fakeChannel);
        $fakeArticle->channel->reader_view_only = false;

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findBy')
            ->once()
            ->with('uniqueId', $fakeArticle->uniqueID)
            ->andReturn($fakeArticle);

        $translateHtmlContentMock = Mockery::mock(TranslateHtmlContent::class);
        $translateHtmlContentMock->shouldNotReceive('execute');

        $translateTextMock = Mockery::mock(TranslateText::class);
        $translateTextMock->shouldNotReceive('execute');

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldNotReceive('updateOrCreate');

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldNotReceive('getArticleHtmlCacheKey');

        $translateArticlesLogic = new TranslateArticlesLogic(
            $translateHtmlContentMock,
            $articleRepositoryMock,
            $translateTextMock,
            $articleContentMock,
            $cacheKeyServiceMock
        );

        $translateArticlesLogic->execute($fakeArticle->uniqueID);
    }

    public function testThrowNwServiceException() : void
    {
        $this->expectException(NwServiceException::class);

        $fakeArticle = Article::factory()->make();
        $fakeChannel = Channel::factory()->make();
        $fakeArticle->setRelation('channel', $fakeChannel);
        $fakeArticle->channel->reader_view_only = true;

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findBy')
            ->with('uniqueId', $fakeArticle->uniqueID)
            ->andReturn($fakeArticle);

        $translateHtmlContentMock = Mockery::mock(TranslateHtmlContent::class);
        $translateHtmlContentMock->shouldReceive('execute')
            ->andThrow(NwServiceException::class);

        $translateTextMock = Mockery::mock(TranslateText::class);
        $translateTextMock->shouldNotReceive('execute');

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldNotReceive('updateOrCreate');

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldNotReceive('getArticleHtmlCacheKey');

        $translateArticlesLogic = new TranslateArticlesLogic(
            $translateHtmlContentMock,
            $articleRepositoryMock,
            $translateTextMock,
            $articleContentMock,
            $cacheKeyServiceMock
        );

        $translateArticlesLogic->execute($fakeArticle->uniqueID);
    }
}
