<?php


use App\Services\Cache\CacheKeyService;
use App\V4\Common\Constants;
use App\V4\Models\ArticleContent;
use App\V4\Modules\Articles\Services\RetrievesArticleTranslationFromCache;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use App\V4\ValueObjects\ArticleTranslationObject;

class RetrievesArticleHtmlFromCacheTest extends TestCase
{
    public function testRetrieveArticleHtmlWhenCached() : void
    {
        $language = Constants::EN;
        $fakeId = $this->generator->uuid;
        $fakeText = $this->generator->text;
        $fakeTitle = $this->generator->title;
        $articleData = Mockery::mock(); // generic object
        $cacheKey = "article-html-$fakeId-$language";
        $fakeArticleTranslationObject = new ArticleTranslationObject(
            $language, $fakeText, $fakeTitle
        );

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getArticleHtmlCacheKey')
            ->once()
            ->with($fakeId, $language)
            ->andReturn($cacheKey);

        // Mock cache
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
                )
            ->andReturn($fakeArticleTranslationObject);
        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldNotReceive('where');

        $service = new RetrievesArticleTranslationFromCache($cacheKeyServiceMock, $articleContentMock);
        $result = $service->execute($language, $fakeId, $articleData);

        $this->assertEquals($fakeArticleTranslationObject->getLanguage(), $result->getLanguage());
        $this->assertEquals($fakeArticleTranslationObject->getTranslatedContent(), $result->getTranslatedContent());
        $this->assertEquals($fakeArticleTranslationObject->getTranslatedTitle(), $result->getTranslatedTitle());
    }

    public function testRetrieveArticleHtmlWhenNotCached() : void
    {
        $language = Constants::EN;
        $fakeId = $this->generator->uuid;

        $fakeText = $this->generator->text;
        $fakeTranslatedText = $this->generator->text;

        $fakeTitle = $this->generator->title;
        $fakeTranslatedTitle = $this->generator->text;

        $articleData = (object)[
            'title' => $fakeTitle,
            'meta' => (object)[
                'article' => (object)[
                    'language' => Constants::ZH,
                    'content' => $fakeText,
                ],
            ],
        ];
        $cacheKey = "article-html-$fakeId-$language";

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getArticleHtmlCacheKey')
            ->once()
            ->with($fakeId, $language)
            ->andReturn($cacheKey);

        // Mock cache fallback
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
            )
            ->andReturnUsing(function ($key, $time, $closure) {
                return $closure();
            });

        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldReceive('where')
            ->with('unique_id', $fakeId)
            ->once()
            ->andReturnSelf();
        $articleContentMock->shouldReceive('where')
            ->with('language', $language)
            ->once()
            ->andReturnSelf();
        $articleContentMock->shouldReceive('firstOrFail')
            ->once()
            ->andReturn((object)[
                'html' => $fakeTranslatedText,
                'title' => $fakeTranslatedTitle
            ]);

        $service = new RetrievesArticleTranslationFromCache($cacheKeyServiceMock, $articleContentMock);
        $result = $service->execute($language, $fakeId, $articleData);

        $this->assertEquals($fakeTranslatedText, $result->getTranslatedContent());
        $this->assertEquals($fakeTranslatedTitle, $result->getTranslatedTitle());
    }

    public function testThrowModelNotFoundException() : void
    {
        $language = Constants::EN;
        $fakeId = $this->generator->uuid;
        $fakeText = $this->generator->text;
        $fakeTitle = $this->generator->title;
        $articleData = (object)[
            'title' => $fakeTitle,
            'meta' => (object)[
                'article' => (object)[
                    'language' => Constants::ZH,
                    'content' => $fakeText,
                ],
            ],
        ];
        $cacheKey = "article-html-$fakeId-$language";

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getArticleHtmlCacheKey')
            ->once()
            ->with($fakeId, $language)
            ->andReturn($cacheKey);

        // Mock cache fallback
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
            )
            ->andReturnUsing(function ($key, $time, $closure) {
                return $closure();
            });

        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldReceive('where')
            ->with('unique_id', $fakeId)
            ->once()
            ->andReturnSelf();
        $articleContentMock->shouldReceive('where')
            ->with('language', $language)
            ->once()
            ->andReturnSelf();
        $articleContentMock->shouldReceive('firstOrFail')
            ->once()
            ->andThrow(new ModelNotFoundException);

        $service = new RetrievesArticleTranslationFromCache($cacheKeyServiceMock, $articleContentMock);
        $result = $service->execute($language, $fakeId, $articleData);

        $this->assertEquals($fakeText, $result->getTranslatedContent());
    }

    public function testRetrieveArticleHtmlWhenNotCachedAndSameLanguage() : void
    {
        $language = Constants::EN;
        $fakeId = $this->generator->uuid;
        $fakeText = $this->generator->text;
        $fakeTitle = $this->generator->title;
        $articleData = (object)[
            'title' => $fakeTitle,
            'meta' => (object)[
                'article' => (object)[
                    'language' => Constants::EN,
                    'content' => $fakeText,
                ],
            ],
        ];
        $cacheKey = "article-html-$fakeId-$language";

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getArticleHtmlCacheKey')
            ->once()
            ->with($fakeId, $language)
            ->andReturn($cacheKey);

        // Mock cache fallback
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
            )
            ->andReturnUsing(function ($key, $time, $closure) {
                return $closure();
            });

        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $articleContentMock = Mockery::mock(ArticleContent::class);
        $articleContentMock->shouldNotReceive('where');

        $service = new RetrievesArticleTranslationFromCache($cacheKeyServiceMock, $articleContentMock);
        $result = $service->execute($language, $fakeId, $articleData);

        $this->assertEquals($fakeText, $result->getTranslatedContent());
    }
}
