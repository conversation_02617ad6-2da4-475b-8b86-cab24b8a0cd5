<?php

use Tests\TestCase;
use App\V4\Modules\Articles\Services\RetrievesArticleFromCache;
use App\Services\Cache\CacheKeyService;
use App\V4\Models\NwUser;
use App\V4\Services\FeedService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;


class RetrievesArticleFromCacheTest extends TestCase
{
    public function testRetrieveArticleWhenCached() : void
    {
        $fakeId = $this->generator->uuid;
        $mockUser = Mockery::mock(NwUser::class);
        $cacheKey = "api-article-content-$fakeId";
        $articleData = Mockery::mock(); // generic object

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getApiArticleContentCacheKey')
            ->once()
            ->with($fakeId)
            ->andReturn($cacheKey);

        $feedServiceMock = Mockery::mock(FeedService::class);
        $feedServiceMock->shouldNotReceive('getArticle');

        // Mock cache
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
                )
            ->andReturn($articleData);
        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $retrievesArticleFromCache = new RetrievesArticleFromCache($cacheKeyServiceMock, $feedServiceMock);
        $result = $retrievesArticleFromCache->execute($fakeId, $mockUser);

        $this->assertEquals($articleData, $result);
    }

    public function testRetrieveArticleWhenNotCached() : void
    {
        $fakeId = $this->generator->uuid;
        $mockUser = Mockery::mock(NwUser::class);
        $cacheKey = "api-article-content-$fakeId";
        $articleData = Mockery::mock(); // generic object

        $cacheKeyServiceMock = Mockery::mock(CacheKeyService::class);
        $cacheKeyServiceMock->shouldReceive('getApiArticleContentCacheKey')
            ->once()
            ->with($fakeId)
            ->andReturn($cacheKey);

        $feedServiceMock = Mockery::mock(FeedService::class);
        $feedServiceMock->shouldReceive('getArticle')
            ->with($mockUser, $fakeId)
            ->once()
            ->andReturn($articleData);

        // Mock cache fallback
        $articleCacheMock = Mockery::mock();
        $articleCacheMock->shouldReceive('remember')
            ->once()
            ->with(
                $cacheKey,
                Mockery::type(Carbon::class),
                Mockery::type(Closure::class)
            )
            ->andReturnUsing(function ($key, $time, $closure) {
                return $closure();
            });

        Cache::shouldReceive('store')
            ->with('redis')
            ->once()
            ->andReturn($articleCacheMock);

        $retrievesArticleFromCache = new RetrievesArticleFromCache($cacheKeyServiceMock,$feedServiceMock);

        $result = $retrievesArticleFromCache->execute($fakeId, $mockUser);

        $this->assertEquals($articleData, $result);
    }
}
