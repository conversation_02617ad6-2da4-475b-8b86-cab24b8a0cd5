<?php

namespace Tests\Unit\V4\Modules\Articles\Logics;

use App\V4\Common\Constants;
use App\V4\Modules\Articles\Logics\PrepareArticleContentLogic;
use App\V4\Models\NwUser;
use App\V4\Modules\Articles\Services\RetrievesArticleFromCache;
use App\V4\Modules\Articles\Services\RetrievesArticleTranslationFromCache;
use App\V4\ValueObjects\ArticleTranslationObject;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;
use Mockery;

class PrepareArticleContentLogicTest extends TestCase
{

    public function testPrepareTranslatedArticleContent() : void
    {
        $fakeHtml = $this->generator->text;
        $fakeTitle = $this->generator->title;
        $fakeTranslatedHtml = $this->generator->text;
        $fakeTranslatedTitle = $this->generator->title;
        $mainLanguage = Constants::EN;
        $articleLanguage = Constants::MS;
        $fakeId = $this->generator->uuid;
        $fakePublisher = $this->generator->text;
        $showOriginal = false;

        $fakeArticleTranslation = new ArticleTranslationObject(
            $articleLanguage, $fakeTranslatedHtml, $fakeTranslatedTitle
        );

        $articleData = (object) [
            'title' => $fakeTitle,
            'meta' => (object) [
                'article' => (object) [
                    'language' => $articleLanguage,
                    'content' => $fakeHtml,
                ],
            ],
            'publisher' => (object) [
                'id' => $fakePublisher,
                'isFollowing' => false,
            ],
        ];

        $mockUser = Mockery::mock(NwUser::class);
        $mockUser->shouldReceive('getMainLanguage')->once()->andReturn($mainLanguage);
        $mockUser->shouldReceive('getLanguages')->once()->andReturn([Constants::EN, Constants::ZH]);
        $mockUser->shouldReceive('getUserFollowingPublishers')->once()->andReturn([]);

        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $retrievesArticleFromCacheMock = Mockery::mock(RetrievesArticleFromCache::class);
        $retrievesArticleFromCacheMock->shouldReceive('execute')
            ->once()
            ->with($fakeId, $mockUser)
            ->andReturn($articleData);

        $retrievesArticleHtmlFromCacheMock = Mockery::mock(RetrievesArticleTranslationFromCache::class);
        $retrievesArticleHtmlFromCacheMock->shouldReceive('execute')
            ->once()
            ->with($mainLanguage, $fakeId, $articleData)
            ->andReturn($fakeArticleTranslation);

        $prepareArticleContentLogic = new PrepareArticleContentLogic($retrievesArticleFromCacheMock, $retrievesArticleHtmlFromCacheMock);

        $result = $prepareArticleContentLogic->execute($fakeId, $showOriginal);

        $this->assertEquals($fakeTranslatedHtml, $result->meta->article->content);
        $this->assertEquals($fakeTranslatedTitle, $result->title);
        $this->assertTrue(isset($result->meta->article->is_translated));
        $this->assertFalse($articleData->publisher->isFollowing);
    }

    public function testForVideoContent() : void
    {
        $mainLanguage = Constants::EN;
        $fakeId = $this->generator->uuid;
        $fakePublisher = $this->generator->text;
        $showOriginal = false;

        $videoData = (object) [
            'meta' => (object) [
                'video' => (object) [
                    'mimeType' => "video/mp4",
                ],
            ],
            'publisher' => (object) [
                'id' => $fakePublisher,
                'isFollowing' => false,
            ],
        ];

        $mockUser = Mockery::mock(NwUser::class);
        $mockUser->shouldReceive('getMainLanguage')->once()->andReturn($mainLanguage);
        $mockUser->shouldReceive('getLanguages')->once()->andReturn([Constants::EN, Constants::ZH]);
        $mockUser->shouldReceive('getUserFollowingPublishers')->once()->andReturn([]);

        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $retrievesArticleFromCacheMock = Mockery::mock(RetrievesArticleFromCache::class);
        $retrievesArticleFromCacheMock->shouldReceive('execute')
            ->once()
            ->with($fakeId, $mockUser)
            ->andReturn($videoData);

        $retrievesArticleHtmlFromCacheMock = Mockery::mock(RetrievesArticleTranslationFromCache::class);
        $retrievesArticleHtmlFromCacheMock->shouldNotReceive('execute');

        $prepareArticleContentLogic = new PrepareArticleContentLogic($retrievesArticleFromCacheMock, $retrievesArticleHtmlFromCacheMock);

        $result = $prepareArticleContentLogic->execute($fakeId, $showOriginal);

        $this->assertEquals("video/mp4", $result->meta->video->mimeType);
    }

    public function testUserIsFollowingPublisher() : void
    {
        $fakeTitle = $this->generator->title;
        $fakeHtml = $this->generator->text;
        $mainLanguage = Constants::EN;
        $articleLanguage = Constants::EN;
        $fakeId = $this->generator->uuid;
        $fakePublisher = $this->generator->text;
        $showOriginal = false;

        $fakeArticleTranslation = new ArticleTranslationObject(
            $articleLanguage, $fakeHtml, $fakeTitle
        );

        $articleData = (object) [
            'meta' => (object) [
                'article' => (object) [
                    'language' => $articleLanguage,
                    'content' => $fakeHtml,
                ],
            ],
            'publisher' => (object) [
                'id' => $fakePublisher,
                'isFollowing' => true,
            ],
        ];

        $mockUser = Mockery::mock(NwUser::class);
        $mockUser->shouldReceive('getMainLanguage')->once()->andReturn($mainLanguage);
        $mockUser->shouldReceive('getLanguages')->once()->andReturn([Constants::EN, Constants::ZH]);
        $mockUser->shouldReceive('getUserFollowingPublishers')->once()->andReturn([$fakePublisher]);

        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $retrievesArticleFromCacheMock = Mockery::mock(RetrievesArticleFromCache::class);
        $retrievesArticleFromCacheMock->shouldReceive('execute')
            ->once()
            ->with($fakeId, $mockUser)
            ->andReturn($articleData);

        $retrievesArticleHtmlFromCacheMock = Mockery::mock(RetrievesArticleTranslationFromCache::class);
        $retrievesArticleHtmlFromCacheMock->shouldReceive('execute')
            ->once()
            ->with($articleLanguage, $fakeId, $articleData)
            ->andReturn($fakeArticleTranslation);

        $prepareArticleContentLogic = new PrepareArticleContentLogic($retrievesArticleFromCacheMock, $retrievesArticleHtmlFromCacheMock);

        $prepareArticleContentLogic->execute($fakeId, $showOriginal);

        $this->assertTrue($articleData->publisher->isFollowing);
    }
}
