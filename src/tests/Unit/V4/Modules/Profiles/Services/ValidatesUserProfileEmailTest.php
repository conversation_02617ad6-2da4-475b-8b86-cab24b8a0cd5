<?php

namespace V4\Modules\Profiles\Services;

use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Models\UserProfile;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileEmail;
use App\V4\Repositories\UserProfileRepository;
use Tests\TestCase;
use Mockery;

class ValidatesUserProfileEmailTest extends TestCase
{

    public function testItValidatesUserProfileUsername(): void {
        $fakeEmail = $this->generator->email;

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldReceive('getUserProfileByEmail')
            ->once()
            ->with($fakeEmail)
            ->andReturnNull();

        $service = new ValidatesUserProfileEmail($userProfileRepositoryMock);
        $service->execute($fakeEmail);
    }

    public function testItThrowsNwBadRequestExceptionForEmailExists(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeEmail = $this->generator->email;
        $fakeUserProfile = UserProfile::factory()->make();

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldReceive('getUserProfileByEmail')
            ->once()
            ->with($fakeEmail)
            ->andReturn($fakeUserProfile);

        $service = new ValidatesUserProfileEmail($userProfileRepositoryMock);
        $service->execute($fakeEmail);
    }
}
