<?php

namespace Tests\Unit\V4\Modules\Profiles\Services;

use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Models\AutoReportKeyword;
use App\V4\Models\UserProfile;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileUsername;
use App\V4\Repositories\AutoReportKeywordRepository;
use App\V4\Repositories\UserProfileRepository;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class ValidatesUserProfileUsernameTest extends TestCase
{
    public function testItValidatesUserProfileUsername(): void {
        $fakeUsername = $this->generator->userName;
        $fakeAutoReportKeyword = AutoReportKeyword::factory()->make([
            'value' => '["anjing"]',
            'type' => AutoReportKeyword::TYPE_HIDE
        ]);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldReceive('getUserProfileByUsername')
            ->once()
            ->with($fakeUsername)
            ->andReturnNull();

        $autoReportKeywordRepositoryMock = Mockery::mock(AutoReportKeywordRepository::class);
        $autoReportKeywordRepositoryMock->shouldReceive('getHiddenKeywords')
            ->once()
            ->with()
            ->andReturn($fakeAutoReportKeyword);

        $service = new ValidatesUserProfileUsername($userProfileRepositoryMock, $autoReportKeywordRepositoryMock);
        $service->execute($fakeUsername);
    }

    public function testItThrowsNwBadRequestExceptionForInvalidFormat(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeUsername = sprintf(' %s', $this->generator->userName);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('getUserProfileByUsername');

        $autoReportKeywordRepositoryMock = Mockery::mock(AutoReportKeywordRepository::class);
        $autoReportKeywordRepositoryMock->shouldNotReceive('getHiddenKeywords');

        $service = new ValidatesUserProfileUsername($userProfileRepositoryMock, $autoReportKeywordRepositoryMock);
        $service->execute($fakeUsername);
    }

    public function testItThrowsNwBadRequestExceptionForProhibitedKeyword(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeUsername = "anjing";
        $fakeAutoReportKeyword = AutoReportKeyword::factory()->make([
            'value' => '["anjing"]',
            'type' => AutoReportKeyword::TYPE_HIDE
        ]);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('getUserProfileByUsername');

        $autoReportKeywordRepositoryMock = Mockery::mock(AutoReportKeywordRepository::class);
        $autoReportKeywordRepositoryMock->shouldReceive('getHiddenKeywords')
            ->once()
            ->with()
            ->andReturn($fakeAutoReportKeyword);

        $service = new ValidatesUserProfileUsername($userProfileRepositoryMock, $autoReportKeywordRepositoryMock);
        $service->execute($fakeUsername);
    }

    public function testItThrowsNwBadRequestExceptionForUsernameExists(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeUsername = $this->generator->userName;
        $fakeAutoReportKeyword = AutoReportKeyword::factory()->make([
            'value' => '["anjing"]',
            'type' => AutoReportKeyword::TYPE_HIDE
        ]);
        $fakeUserProfile = UserProfile::factory()->make();

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldReceive('getUserProfileByUsername')
            ->once()
            ->with($fakeUsername)
            ->andReturn($fakeUserProfile);

        $autoReportKeywordRepositoryMock = Mockery::mock(AutoReportKeywordRepository::class);
        $autoReportKeywordRepositoryMock->shouldReceive('getHiddenKeywords')
            ->once()
            ->with()
            ->andReturn($fakeAutoReportKeyword);

        $service = new ValidatesUserProfileUsername($userProfileRepositoryMock, $autoReportKeywordRepositoryMock);
        $service->execute($fakeUsername);
    }
}
