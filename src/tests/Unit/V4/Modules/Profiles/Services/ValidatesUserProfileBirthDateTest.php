<?php

namespace V4\Modules\Profiles\Services;

use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileBirthDate;
use Tests\TestCase;
use Carbon\Carbon;

class ValidatesUserProfileBirthDateTest extends TestCase
{
    public function testItValidatesUserProfileBirthDate(): void {
        Carbon::setTestNow('2024/01/12');
        $service = new ValidatesUserProfileBirthDate();

        $fakeBirthDate = '2011/01/12';
        $response = $service->execute($fakeBirthDate);
        static::assertEmpty($response);

        $fakeBirthDate = null;
        $response = $service->execute($fakeBirthDate);
        static::assertEmpty($response);
    }

    public function testItThrowsNwBadRequestExceptionV4(): void {
        Carbon::setTestNow('2024/01/12');

        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeBirthDate = '2011/01/13';
        $service = new ValidatesUserProfileBirthDate();
        $service->execute($fakeBirthDate);
    }

    public function testItThrowsNwBadRequestException2V4(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeBirthDate = '2011/0113';
        $service = new ValidatesUserProfileBirthDate();
        $service->execute($fakeBirthDate);
    }
}
