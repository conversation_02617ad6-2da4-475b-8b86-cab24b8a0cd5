<?php

namespace Tests\Unit\V4\Modules\Profiles\Logics;

use App\Services\Newswav\UserProfile\UserProfileService;
use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Http\Requests\Profiles\FirebaseIdRequestV4;
use App\V4\Models\UserFollower;
use App\V4\Models\UserLogin;
use App\V4\Modules\Profiles\Logics\UnfollowUserLogic;
use App\V4\Modules\Users\Services\RetrievesUserDeviceProfileSummaries;
use App\V4\Modules\Users\ValueObjects\UserDeviceProfileSummary;
use App\V4\Repositories\UserFollowerRepository;
use App\V4\Repositories\UserLoginRepository;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;
use Mockery;

class UnfollowUserLogicTest extends TestCase
{
    public function testItUnfollowUser(): void
    {
        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerProfileId = $this->generator->uuid;
        $fakeFollowerFirebaseId = $this->generator->uuid;

        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeProfileId = $this->generator->uuid;
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);

        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);
        $fakeUserFollower = UserFollower::factory()->make([
            'user_id' => $fakeFolloweeId,
            'follower_id' => $fakeFollowerId,
        ]);
        $followeeUserDeviceProfiles = [
            new UserDeviceProfileSummary($fakeFolloweeProfileId, $fakeFolloweeFirebaseId)
        ];

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeFollowerProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFollowerProfileId, $fakeFollowerFirebaseId, 'following_decrement')
            ->andReturnNull();
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFolloweeProfileId, $fakeFolloweeFirebaseId, 'follower_decrement')
            ->andReturnNull();

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturn($fakeUserFollower);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($followeeUserDeviceProfiles);

        $logic = new UnfollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItThrowsUserNotFoundException(): void
    {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturnNull();
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldNotReceive('getUserFollowerByFollowerId');
        $userFollowerRepositoryMock
            ->shouldNotReceive('createUserFollower');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldNotReceive('getUserLoginFromProviderUid');

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $logic = new UnfollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwBadRequestException(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeFirebaseId = $this->generator->uuid;

        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);

        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturnNull();

        $logic = new UnfollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }
}
