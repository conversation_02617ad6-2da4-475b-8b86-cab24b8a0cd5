<?php

namespace Tests\Unit\V4\Modules\Profiles\Logics;

use App\Events\UserFollowerAddedEvent;
use App\Events\UserProfileUpdatedEvent;
use App\Services\Newswav\UserProfile\UserProfileService;
use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Http\Requests\Profiles\FirebaseIdRequestV4;
use App\V4\Models\UserFollower;
use App\V4\Models\UserLogin;
use App\V4\Models\UserProfile;
use App\V4\Modules\Profiles\Logics\FollowUserLogic;
use App\V4\Modules\Users\Services\RetrievesUserDeviceProfileSummaries;
use App\V4\Modules\Users\ValueObjects\UserDeviceProfileSummary;
use App\V4\Repositories\UserFollowerRepository;
use App\V4\Repositories\UserLoginRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Mockery;

class FollowUserLogicTest extends TestCase
{
    public function testItFollowUser(): void
    {
        Event::fake();
        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerProfileId = $this->generator->uuid;
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);
        $fakeFollowerUserProfile = UserProfile::factory()->make([
            'user_id' =>  $fakeFollowerId
        ]);
        $fakeFollowerUserLogin->setRelation('userProfile', $fakeFollowerUserProfile);

        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeProfileId = $this->generator->uuid;
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);
        $fakeFolloweeUserProfile = UserProfile::factory()->make([
            'user_id' =>  $fakeFolloweeId
        ]);
        $fakeFolloweeUserLogin->setRelation('userProfile', $fakeFolloweeUserProfile);
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);
        $fakeUserFollower = UserFollower::factory()->make([
            'user_id' => $fakeFolloweeId,
            'follower_id' => $fakeFollowerId,
        ]);
        $followeeUserDeviceProfiles = [
            new UserDeviceProfileSummary($fakeFolloweeProfileId, $fakeFolloweeFirebaseId)
        ];

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeFollowerProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturnNull();
        $userFollowerRepositoryMock
            ->shouldReceive('createUserFollower')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturn($fakeUserFollower);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFollowerProfileId, $fakeFollowerFirebaseId, 'following_increment')
            ->andReturnNull();
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFolloweeProfileId, $fakeFolloweeFirebaseId, 'follower_increment')
            ->andReturnNull();

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($followeeUserDeviceProfiles);

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
        Event::assertDispatched(UserFollowerAddedEvent::class);
    }

    public function testItFollowProfileIncompleteUser(): void
    {
        Event::fake();
        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerProfileId = $this->generator->uuid;
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);
        $fakeFollowerUserProfile = UserProfile::factory()->make([
            'user_id' =>  $fakeFollowerId
        ]);
        $fakeFollowerUserLogin->setRelation('userProfile', $fakeFollowerUserProfile);

        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeProfileId = $this->generator->uuid;
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);
        $fakeUserFollower = UserFollower::factory()->make([
            'user_id' => $fakeFolloweeId,
            'follower_id' => $fakeFollowerId,
        ]);
        $followeeUserDeviceProfiles = [
            new UserDeviceProfileSummary($fakeFolloweeProfileId, $fakeFolloweeFirebaseId)
        ];

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeFollowerProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturnNull();
        $userFollowerRepositoryMock
            ->shouldReceive('createUserFollower')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturn($fakeUserFollower);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFollowerProfileId, $fakeFollowerFirebaseId, 'following_increment')
            ->andReturnNull();
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFolloweeProfileId, $fakeFolloweeFirebaseId, 'follower_increment')
            ->andReturnNull();

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($followeeUserDeviceProfiles);

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
        Event::assertDispatched(UserFollowerAddedEvent::class);
    }

    public function testItThrowsUserNotFoundException(): void
    {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturnNull();
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldNotReceive('getUserFollowerByFollowerId');
        $userFollowerRepositoryMock
            ->shouldNotReceive('createUserFollower');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldNotReceive('getUserLoginFromProviderUid');

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwBadRequestExceptionV4WhenUserFollowThemselves(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFollowerFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldNotReceive('getUserFollowerByFollowerId');
        $userFollowerRepositoryMock
            ->shouldNotReceive('createUserFollower');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItThrowsUserNotFoundExceptionWhenFolloweeUserNotFound(): void
    {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);

        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldNotReceive('getUserFollowerByFollowerId');
        $userFollowerRepositoryMock
            ->shouldNotReceive('createUserFollower');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturnNull();
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwBadRequestExceptionV4WhenUserFollowDuplicate(): void
    {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);

        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);
        $fakeUserFollower = UserFollower::factory()->make([
            'user_id' => $fakeFolloweeId,
            'follower_id' => $fakeFollowerId,
        ]);
        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturn($fakeUserFollower);
        $userFollowerRepositoryMock
            ->shouldNotReceive('createUserFollower');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldNotReceive('execute');

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
    }

    public function testItProfileIncompleteUserFollowOthers(): void
    {
        Event::fake();
        // Complete Profile Followee
        $fakeFolloweeId = $this->generator->randomNumber(2);
        $fakeFolloweeProfileId = $this->generator->uuid;
        $fakeFolloweeFirebaseId = $this->generator->uuid;
        $fakeFolloweeUserLogin = UserLogin::factory()->make([
            'id' => $fakeFolloweeId,
            'loginProviderUID' => $fakeFolloweeFirebaseId
        ]);
        $fakeFolloweeUserProfile = UserProfile::factory()->make([
            'user_id' =>  $fakeFolloweeId
        ]);
        $fakeFolloweeUserLogin->setRelation('userProfile', $fakeFolloweeUserProfile);

        // Incomplete Profile Follower
        $fakeFollowerId = $this->generator->randomNumber(2);
        $fakeFollowerProfileId = $this->generator->uuid;
        $fakeFollowerFirebaseId = $this->generator->uuid;
        $fakeFollowerUserLogin = UserLogin::factory()->make([
            'id' => $fakeFollowerId,
            'loginProviderUID' => $fakeFollowerFirebaseId
        ]);

        $fakeRequest = new FirebaseIdRequestV4();
        $fakeRequest->merge([
            'firebase_id' => $fakeFolloweeFirebaseId,
        ]);
        $fakeUserFollower = UserFollower::factory()->make([
            'user_id' => $fakeFolloweeId,
            'follower_id' => $fakeFollowerId,
        ]);
        $followeeUserDeviceProfiles = [
            new UserDeviceProfileSummary($fakeFolloweeProfileId, $fakeFolloweeFirebaseId)
        ];

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeFollowerId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeFollowerProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFollowerFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $userFollowerRepositoryMock = Mockery::mock(UserFollowerRepository::class);
        $userFollowerRepositoryMock
            ->shouldReceive('getUserFollowerByFollowerId')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturnNull();
        $userFollowerRepositoryMock
            ->shouldReceive('createUserFollower')
            ->once()
            ->with($fakeFolloweeId, $fakeFollowerId)
            ->andReturn($fakeUserFollower);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFollowerProfileId, $fakeFollowerFirebaseId, 'following_increment')
            ->andReturnNull();
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeFolloweeProfileId, $fakeFolloweeFirebaseId, 'follower_increment')
            ->andReturnNull();

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($fakeFolloweeUserLogin);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($fakeFollowerFirebaseId)
            ->andReturn($fakeFollowerUserLogin);

        $retrievesUserDeviceProfileSummariesMock = Mockery::mock(RetrievesUserDeviceProfileSummaries::class);
        $retrievesUserDeviceProfileSummariesMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeFolloweeFirebaseId)
            ->andReturn($followeeUserDeviceProfiles);

        $logic = new FollowUserLogic(
            $userFollowerRepositoryMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock,
            $retrievesUserDeviceProfileSummariesMock
        );
        $logic->execute($fakeRequest);
        Event::assertDispatched(UserFollowerAddedEvent::class);
    }
}
