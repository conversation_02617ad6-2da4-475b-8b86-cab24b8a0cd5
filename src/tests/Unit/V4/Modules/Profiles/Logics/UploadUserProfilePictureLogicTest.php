<?php

namespace V4\Modules\Profiles\Logics;

use App\Events\UserProfileUpdatedEvent;
use App\Modules\Uploads\Services\CloudStorageService;
use App\Services\Newswav\UserProfile\UserProfileService;
use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Models\UserProfile;
use App\V4\Modules\Profiles\Logics\UploadUserProfilePictureLogic;
use App\V4\Repositories\UserProfileRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Mockery;
use Illuminate\Http\UploadedFile;
use App\V4\Http\Requests\Profiles\UploadProfilePictureRequestV4;

class UploadUserProfilePictureLogicTest extends TestCase
{
    public function testItUploadUserProfilePicture(): void {
        Event::fake();

        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeFile = UploadedFile::fake()->image('profile_picture.jpg');
        $fakeUrl = $this->generator->url;
        $fakeUserProfile = UserProfile::factory()->make();

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $cloudStorageServiceMock = Mockery::mock(CloudStorageService::class);
        $cloudStorageServiceMock
            ->shouldReceive('uploadToCloud')
            ->once()
            ->with(Mockery::type('string'), $fakeFile->getPathname())
            ->andReturn($fakeUrl);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('updateProfilePicture')
            ->once()
            ->with($fakeUserId, $fakeUrl);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeProfileId, $fakeFirebaseId, 'upload_picture');
        $userProfileRepositoryMock
            ->shouldReceive('getUserProfileByUserLoginId')
            ->once()
            ->with($fakeUserId)
            ->andReturn($fakeUserProfile);

        $fakeRequest = new UploadProfilePictureRequestV4();
        $fakeRequest->files->set('profile_picture', $fakeFile);

        $logic = new UploadUserProfilePictureLogic(
            $userProfileRepositoryMock, $cloudStorageServiceMock, $userProfileServiceMock);
        $logic->execute($fakeRequest);
        Event::assertDispatched(UserProfileUpdatedEvent::class);
    }

    public function testItThrowsUserNotFoundExceptionV4ForInvalidUser(): void {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeFile = UploadedFile::fake()->image('profile_picture.jpg');
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturnNull();
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $cloudStorageServiceMock = Mockery::mock(CloudStorageService::class);
        $cloudStorageServiceMock->shouldNotReceive('uploadToCloud');

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('updateProfilePicture');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');
        $userProfileServiceMock->shouldNotReceive('getUserProfileByUserLoginId');

        $fakeRequest = new UploadProfilePictureRequestV4();
        $fakeRequest->files->set('profile_picture', $fakeFile);

        $logic = new UploadUserProfilePictureLogic(
            $userProfileRepositoryMock, $cloudStorageServiceMock, $userProfileServiceMock);
        $logic->execute($fakeRequest);
    }

    public function testItThrowsUserNotFoundExceptionV4ForProfileIncomplete(): void {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeFile = UploadedFile::fake()->image('profile_picture.jpg');
        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);
        $fakeUrl = $this->generator->url;

        $cloudStorageServiceMock = Mockery::mock(CloudStorageService::class);
        $cloudStorageServiceMock
            ->shouldReceive('uploadToCloud')
            ->once()
            ->with(Mockery::type('string'), $fakeFile->getPathname())
            ->andReturn($fakeUrl);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('updateProfilePicture')
            ->once()
            ->with($fakeUserId, $fakeUrl)
            ->andThrow(new ModelNotFoundException());

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');
        $userProfileServiceMock->shouldNotReceive('getUserProfileByUserLoginId');

        $fakeRequest = new UploadProfilePictureRequestV4();
        $fakeRequest->files->set('profile_picture', $fakeFile);

        $logic = new UploadUserProfilePictureLogic(
            $userProfileRepositoryMock, $cloudStorageServiceMock, $userProfileServiceMock);
        $logic->execute($fakeRequest);
    }
}
