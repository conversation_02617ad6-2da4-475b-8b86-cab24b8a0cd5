<?php

namespace V4\Modules\Profiles\Logics;

use App\Events\UserProfileUpdatedEvent;
use App\Services\Newswav\UserProfile\UserProfileService;
use App\V4\Common\Constants;
use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Http\Requests\Profiles\CompleteProfileRequestV4;
use App\V4\Models\UserLogin;
use App\V4\Models\UserProfile;
use App\V4\Modules\Profiles\Logics\CompleteUserProfileLogic;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileBirthDate;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileEmail;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileUsername;
use App\V4\Repositories\UserLoginRepository;
use App\V4\Repositories\UserProfileRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class CompleteUserProfileLogicTest extends TestCase
{
    public function testItCompleteUserProfileLogic(): void {
        Event::fake();

        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeProfileUrl = $this->generator->url;
        $fakeRequest = new CompleteProfileRequestV4();
        $fakeRequest->merge([
            'username' => $this->generator->userName,
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'email' => $this->generator->email
        ]);
        $fakeUserLogin = UserLogin::factory()->make(['loginPhotoURL' => $fakeProfileUrl]);
        $fakeUserProfile = UserProfile::factory()->make();

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileUsernameMock = Mockery::mock(ValidatesUserProfileUsername::class);
        $validatesUserProfileUsernameMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->username);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->birth_date);

        $validatesUserProfileEmailMock = Mockery::mock(ValidatesUserProfileEmail::class);
        $validatesUserProfileEmailMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->email);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('createFromProfileCompletion')
            ->once()
            ->with($fakeUserId, $fakeRequest->username, $fakeRequest->name,
                $fakeRequest->birth_date, $fakeRequest->gender, $fakeRequest->email, $fakeProfileUrl)
            ->andReturn($fakeUserProfile);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeProfileId, $fakeFirebaseId, 'update_profile');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('find')
            ->once()
            ->with($fakeUserId)
            ->andReturn($fakeUserLogin);

        $logic = new CompleteUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileUsernameMock,
            $validatesUserProfileBirthDateMock,
            $validatesUserProfileEmailMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock
        );

        $logic->execute($fakeRequest);
        Event::assertDispatched(UserProfileUpdatedEvent::class);
    }

    public function testItThrowsUserNotFoundExceptionV4(): void {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeRequest = new CompleteProfileRequestV4();
        $fakeRequest->merge([
            'username' => $this->generator->userName,
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'email' => $this->generator->email
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturnNull();
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileUsernameMock = Mockery::mock(ValidatesUserProfileUsername::class);
        $validatesUserProfileUsernameMock->shouldNotReceive('execute');

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock->shouldNotReceive('execute');

        $validatesUserProfileEmailMock = Mockery::mock(ValidatesUserProfileEmail::class);
        $validatesUserProfileEmailMock->shouldNotReceive('execute');

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('createFromProfileCompletion');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock->shouldNotReceive('find');

        $logic = new CompleteUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileUsernameMock,
            $validatesUserProfileBirthDateMock,
            $validatesUserProfileEmailMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock
        );

        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwBadRequestExceptionV4(): void {
        $this->expectException(NwBadRequestExceptionV4::class);

        $fakeUserId = 123;
        $fakeRequest = new CompleteProfileRequestV4();
        $fakeRequest->merge([
            'username' => $this->generator->userName,
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'email' => $this->generator->email
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileUsernameMock = Mockery::mock(ValidatesUserProfileUsername::class);
        $validatesUserProfileUsernameMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->username)
            ->andThrows(NwBadRequestExceptionV4::class);


        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock->shouldNotReceive('execute');

        $validatesUserProfileEmailMock = Mockery::mock(ValidatesUserProfileEmail::class);
        $validatesUserProfileEmailMock->shouldNotReceive('execute');

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('createFromProfileCompletion');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock->shouldNotReceive('find');

        $logic = new CompleteUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileUsernameMock,
            $validatesUserProfileBirthDateMock,
            $validatesUserProfileEmailMock,
            $userProfileServiceMock,
            $userLoginRepositoryMock
        );

        $logic->execute($fakeRequest);
    }
}
