<?php

namespace V4\Modules\Profiles\Logics;

use App\Services\Newswav\UserProfile\UserProfileService;
use App\V4\Common\Constants;
use App\V4\Exceptions\NwBadRequestExceptionV4;
use App\V4\Exceptions\NwNotFoundExceptionV4;
use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Http\Requests\Profiles\UpdateProfileRequestV4;
use App\V4\Modules\Profiles\Logics\UpdateUserProfileLogic;
use App\V4\Modules\Profiles\Services\ValidatesUserProfileBirthDate;
use App\V4\Repositories\UserProfileRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Mockery;
use Tests\TestCase;

class UpdateUserProfileLogicTest extends TestCase
{
    public function testItUpdateUserProfileLogic(): void {
        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeRequest = new UpdateProfileRequestV4();
        $fakeRequest->merge([
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'is_private' => $this->generator->boolean,
            'mobile_number' => $this->generator->phoneNumber,
            'bio' => $this->generator->text,
            'location' => $this->generator->address,
            'website' => $this->generator->url,
            'qualification' => $this->generator->jobTitle,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->birth_date);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('updateFromArray')
            ->once()
            ->with($fakeUserId, $fakeRequest->all());

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeProfileId, $fakeFirebaseId, 'update_profile');

        $logic = new UpdateUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileBirthDateMock,
            $userProfileServiceMock
        );

        $logic->execute($fakeRequest);
    }

    public function testItUpdateUserProfileForEditableFields(): void {
        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeRequest = new UpdateProfileRequestV4();
        $fakeRequest->merge([
            'name' => $this->generator->name,
            'username' => $this->generator->name,
            'bio' => null
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->birth_date);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('updateFromArray')
            ->once()
            ->with($fakeUserId, [ 'name' => $fakeRequest->get('name'), 'bio' => null,]);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock
            ->shouldReceive('invalidateProfileCache')
            ->once()
            ->with($fakeProfileId, $fakeFirebaseId, 'update_profile');

        $logic = new UpdateUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileBirthDateMock,
            $userProfileServiceMock
        );

        $logic->execute($fakeRequest);
    }

    public function testItUserNotFoundExceptionV4(): void {
        $this->expectException(UserNotFoundExceptionV4::class);

        $fakeRequest = new UpdateProfileRequestV4();
        $fakeRequest->merge([
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'is_private' => $this->generator->boolean,
            'mobile_number' => $this->generator->phoneNumber,
            'bio' => $this->generator->text,
            'location' => $this->generator->address,
            'website' => $this->generator->url,
            'qualification' => $this->generator->jobTitle,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturnNull();
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock->shouldNotReceive('execute');

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('updateFromArray');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');

        $logic = new UpdateUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileBirthDateMock,
            $userProfileServiceMock
        );

        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwBadRequestExceptionV4(): void {
        $this->expectException(NwBadRequestExceptionV4::class);
        $fakeUserId = 123;
        $fakeRequest = new UpdateProfileRequestV4();
        $fakeRequest->merge([
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'is_private' => $this->generator->boolean,
            'mobile_number' => $this->generator->phoneNumber,
            'bio' => $this->generator->text,
            'location' => $this->generator->address,
            'website' => $this->generator->url,
            'qualification' => $this->generator->jobTitle,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->birth_date)
            ->andThrows(NwBadRequestExceptionV4::class);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock->shouldNotReceive('updateFromArray');

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');

        $logic = new UpdateUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileBirthDateMock,
            $userProfileServiceMock
        );

        $logic->execute($fakeRequest);
    }

    public function testItThrowsNwNotFoundExceptionV4(): void {
        $this->expectException(NwNotFoundExceptionV4::class);

        $fakeUserId = $this->generator->uuid;
        $fakeProfileId = $this->generator->uuid;
        $fakeFirebaseId = $this->generator->uuid;
        $fakeRequest = new UpdateProfileRequestV4();
        $fakeRequest->merge([
            'name' => $this->generator->name,
            'birth_date' => $this->generator->date(),
            'gender' => $this->generator->randomElement(Constants::GENDERS),
            'is_private' => $this->generator->boolean,
            'mobile_number' => $this->generator->phoneNumber,
            'bio' => $this->generator->text,
            'location' => $this->generator->address,
            'website' => $this->generator->url,
            'qualification' => $this->generator->jobTitle,
        ]);

        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getNewswavUserId')->andReturn($fakeUserId);
        $mockUser->shouldReceive('getProfileId')->andReturn($fakeProfileId);
        $mockUser->shouldReceive('getFirebaseId')->andReturn($fakeFirebaseId);
        Auth::shouldReceive('user')->once()->andReturn($mockUser);

        $validatesUserProfileBirthDateMock = Mockery::mock(ValidatesUserProfileBirthDate::class);
        $validatesUserProfileBirthDateMock
            ->shouldReceive('execute')
            ->once()
            ->with($fakeRequest->birth_date);

        $userProfileRepositoryMock = Mockery::mock(UserProfileRepository::class);
        $userProfileRepositoryMock
            ->shouldReceive('updateFromArray')
            ->once()
            ->with($fakeUserId, $fakeRequest->all())
            ->andThrows(ModelNotFoundException::class);

        $userProfileServiceMock = Mockery::mock(UserProfileService::class);
        $userProfileServiceMock->shouldNotReceive('invalidateProfileCache');

        $logic = new UpdateUserProfileLogic(
            $userProfileRepositoryMock,
            $validatesUserProfileBirthDateMock,
            $userProfileServiceMock
        );

        $logic->execute($fakeRequest);
    }
}
