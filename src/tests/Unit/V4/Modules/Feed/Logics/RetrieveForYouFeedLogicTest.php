<?php

namespace Tests\Unit\V4\Modules\Feed\Logics;

use App\V4\Common\Constants;
use App\V4\Modules\Feed\Logics\RetrieveForYouFeedLogic;
use App\V4\Modules\Feed\Services\RetrieveDiversifiedFeed;
use App\V4\Repositories\Content\ContentRepository;
use App\V4\Services\RecommendationService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Mockery;
use Tests\TestCase;


class RetrieveForYouFeedLogicTest extends TestCase
{
    /*
        Situation: enough from cache to serve
        Expected:
            - Should call user_{firebaseId}_recommendations cache
            - Should call user_seen_content cache
            - Should filter and serve
    */
    public function testItRetrieveForYouFeedWhenCacheHasEnoughUnseenContent(): void
    {
        
        // Mock firebaseId for testing
        $firebaseId = $this->generator->uuid;

        // Recommendation Cache Mock
        $cachedRecommendationContentIds = collect([]);
        // 11 retrieved content - 1 seen content = 10 (enough)
        for($i = 0; $i < 11; $i++) {
            $cachedRecommendationContentIds->push($this->generator->uuid);
        }
        $recommendationCacheMock = Mockery::mock();
        $recommendationCacheMock->shouldReceive('get')
            ->with(
                sprintf('user_%s_recommendations', $firebaseId),
                Mockery::type(Collection::class)
            )
            ->andReturn($cachedRecommendationContentIds);
        Cache::shouldReceive('store')
            ->with('redis')
            ->andReturn($recommendationCacheMock);

        // User Seen Cache Mock
        $cachedSeenContentIdsForThisWeek = [$cachedRecommendationContentIds->first()];
        $userSeenContentThisWeekCacheMock = Mockery::mock();
        $userSeenContentThisWeekCacheMock->shouldReceive('get')
            ->times(3)
            ->with(Mockery::type('string'), [])
            ->andReturn($cachedSeenContentIdsForThisWeek);
        Cache::shouldReceive('store')
            ->with('redis_feed')
            ->andReturn($userSeenContentThisWeekCacheMock);

        // Not call recommendation service
        $recommendationServiceMock = Mockery::mock(RecommendationService::class);
        $recommendationServiceMock->shouldNotReceive('getByFirebaseId');

        // Not call retrieve diversified feed
        $retrieveDiversifiedFeedMock = Mockery::mock(RetrieveDiversifiedFeed::class);
        $retrieveDiversifiedFeedMock->shouldNotReceive('execute');

        // Call filter and getContentBody
        $contentIds = $cachedRecommendationContentIds
            ->diff($cachedSeenContentIdsForThisWeek)
            ->values();
        $contentRepositoryMock = Mockery::mock(ContentRepository::class);
        $contentRepositoryMock->shouldReceive('getContentBody')->once()
            ->with(
                Mockery::on(function ($argument) use ($contentIds): bool {
                    return $argument->toArray() === $contentIds->toArray();
                }),
                1, 'F_F_-4', ['en', 'ms', 'zh'])
            ->andReturn(collect([]));

        $service = new RetrieveForYouFeedLogic(
            $recommendationServiceMock,
            $retrieveDiversifiedFeedMock,
            $contentRepositoryMock
        );

        // Assume no header/body for mock request
        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getFirebaseId')->andReturn($firebaseId);
        Auth::shouldReceive('user')->andReturn($mockUser);
        $response = $service->execute();

        // assertion / check
        $this->assertInstanceOf(Collection::class, $response);
    }


    /*
        Situation: Not enough from cache, enough from Recommendation Service.
        Expected:
            - Should call user_{firebaseId}_recommendations cache
            - Should call user_seen_content cache
            - Should call RecommendationService and retrieve enough content
            - Should update recommendations cache
            - Should filter and serve
    */
    public function testItRetrieveForYouFeedWhenCacheDoesNotHaveEnoughUnseenContent(): void
    {
        
        // Mock firebaseId for testing
        $firebaseId = $this->generator->uuid;

        // Recommendation Cache Mock
        $cachedContentIds = collect([]);
        // 11 retrieved content - 2 seen content = 9 (not enough)
        for($i = 0; $i < 11; $i++) {
            $cachedContentIds->push($this->generator->uuid);
        }
        $recommendationCacheMock = Mockery::mock();
        $recommendationCacheMock->shouldReceive('get')
            ->with(
                sprintf('user_%s_recommendations', $firebaseId),
                Mockery::type(Collection::class)
            )
            ->andReturn($cachedContentIds);
        Cache::shouldReceive('store')
            ->with('redis')
            ->andReturn($recommendationCacheMock);


        // User Seen Cache Mock
        $cachedSeenContentIds = $cachedContentIds->take(2)->toArray();
        $userSeenContentThisWeekCacheMock = Mockery::mock();
        $userSeenContentThisWeekCacheMock->shouldReceive('get')
            ->times(3)
            ->with(Mockery::type('string'), [])
            ->andReturn($cachedSeenContentIds);
        Cache::shouldReceive('store')
            ->with('redis_feed')
            ->andReturn($userSeenContentThisWeekCacheMock);
        $seenContentIds = collect($cachedSeenContentIds);


        // RecommendationService mock
        // Assume retrieved enough content for one page
        $recommendationServiceResultMock = collect([]);
        for ($i = 0; $i < Constants::NUMBER_OF_CONTENT_IN_FORYOU_PAGE; $i++) {
            $recommendationServiceResultMock->push($this->generator->uuid);
        }
        $recommendationServiceMock = Mockery::mock(RecommendationService::class);
        $recommendationServiceMock->shouldReceive('getByFirebaseId')
            ->once()
            // have to use Mockery::on for collection comparison
            ->with($firebaseId, Mockery::on(function ($argument) use ($seenContentIds): bool {
                return $argument->toArray() === $seenContentIds->toArray();
            }),)
            ->andReturn($recommendationServiceResultMock); 

            
        // Not call retrieve diversified feed
        $retrieveDiversifiedFeedMock = Mockery::mock(RetrieveDiversifiedFeed::class);
        $retrieveDiversifiedFeedMock->shouldNotReceive('execute');

        // Concat and filter
        $contentIds = $cachedContentIds->concat($recommendationServiceResultMock);
        $contentIds = $contentIds->diff($seenContentIds);

        
        // Resave into cache
        $resaveCacheMock = Mockery::mock();
        $resaveCacheMock->shouldReceive('put')
        ->with(Mockery::type('string'), $contentIds);
        Cache::shouldReceive('store')
        ->with('redis_feed')
        ->andReturn($resaveCacheMock);
        

        $servedContentIds = $contentIds
            ->take(Constants::NUMBER_OF_CONTENT_IN_FORYOU_PAGE)
            ->values();
        // getContentBody mock return empty collection
        $contentRepositoryMock = Mockery::mock(ContentRepository::class);
        $contentRepositoryMock->shouldReceive('getContentBody')->once()
            ->with(
                Mockery::on(function ($argument) use ($servedContentIds): bool {
                    return $argument->toArray() === $servedContentIds->toArray();
                }),
                1, 'F_F_-4', ['en', 'ms', 'zh'])
            ->andReturn(collect([]));

        $service = new RetrieveForYouFeedLogic(
            $recommendationServiceMock,
            $retrieveDiversifiedFeedMock,
            $contentRepositoryMock
        );

        // Assume no header/body for mock request
        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getFirebaseId')->andReturn($firebaseId);
        Auth::shouldReceive('user')->andReturn($mockUser);
        $response = $service->execute();

        // assertion / check
        $this->assertInstanceOf(Collection::class, $response);
    }

    /*
        Situation: Not enough from cache,  
                   not enough from Recommendation Service,
                   enough from Diversified Feed.
        Expected:
            - Should call user_{firebaseId}_recommendations cache
            - Should call user_seen_content cache
            - Should call RecommendationService and retrieve not enough content
            - Should call RetrieveDiversifiedFeed and retrieve enough content
            - Should update recommendations cache
            - Should filter and serve
    */
    public function testItRetrieveForYouFeedWhenNotEnoughUnseenContentFromBothCacheAndRecommendationService(): void
    {
        
        // Mock firebaseId for testing
        $firebaseId = $this->generator->uuid;

        // Recommendation Cache Mock
        $cachedContentIds = collect([]);
        // 11 retrieved content - 2 seen content = 9 (not enough)
        for($i = 0; $i < 11; $i++) {
            $cachedContentIds->push($this->generator->uuid);
        }
        $recommendationCacheMock = Mockery::mock();
        $recommendationCacheMock->shouldReceive('get')
            ->with(
                sprintf('user_%s_recommendations', $firebaseId),
                Mockery::type(Collection::class)
            )
            ->andReturn($cachedContentIds);
        Cache::shouldReceive('store')
            ->with('redis')
            ->andReturn($recommendationCacheMock);


        // User Seen Cache Mock
        $cachedSeenContentIds = $cachedContentIds->take(2)->toArray();
        $userSeenContentThisWeekCacheMock = Mockery::mock();
        $userSeenContentThisWeekCacheMock->shouldReceive('get')
            ->times(3)
            ->with(Mockery::type('string'), [])
            ->andReturn($cachedSeenContentIds);
        Cache::shouldReceive('store')
            ->with('redis_feed')
            ->andReturn($userSeenContentThisWeekCacheMock);
        $seenContentIds = collect($cachedSeenContentIds);


        // RecommendationService mock
        // Assume retrieved 0 content
        $recommendationServiceResultMock = collect([]);
        $recommendationServiceMock = Mockery::mock(RecommendationService::class);
        $recommendationServiceMock->shouldReceive('getByFirebaseId')
            ->once()
            // have to use Mockery::on for collection comparison
            ->with($firebaseId, Mockery::on(function ($argument) use ($seenContentIds): bool {
                return $argument->toArray() === $seenContentIds->toArray();
            }),)
            ->andReturn($recommendationServiceResultMock); 

            
        // Call retrieve diversified feed
        $diversifiedFeedResult = collect([]);
        // Assume returned 20 content
        for ($i = 0; $i < 20; $i++) {
            $diversifiedFeedResult->push($this->generator->uuid);
        }
        $retrieveDiversifiedFeedMock = Mockery::mock(RetrieveDiversifiedFeed::class);
        $retrieveDiversifiedFeedMock->shouldReceive('execute')
            ->andReturn($diversifiedFeedResult);

            
        // Concat and filter
        $contentIds = $cachedContentIds->concat($diversifiedFeedResult);
        $contentIds = $contentIds->diff($seenContentIds);
        
        
        // Resave into cache
        $resaveCacheMock = Mockery::mock();
        $resaveCacheMock->shouldReceive('put')
        ->with(Mockery::type('string'), $contentIds->values());
        Cache::shouldReceive('store')
        ->with('redis_feed')
        ->andReturn($resaveCacheMock);
        
        
        $servedContentIds = $contentIds
        ->take(Constants::NUMBER_OF_CONTENT_IN_FORYOU_PAGE)
        ->values();
        // getContentBody mock return empty collection
        $contentRepositoryMock = Mockery::mock(ContentRepository::class);
        $contentRepositoryMock->shouldReceive('getContentBody')->once()
            ->with(
                Mockery::on(function ($argument) use ($servedContentIds): bool {
                    return $argument->toArray() === $servedContentIds->toArray();
                }),
                1, 'F_F_-4', ['en', 'ms', 'zh'])
            ->andReturn(collect([]));

        $service = new RetrieveForYouFeedLogic(
            $recommendationServiceMock,
            $retrieveDiversifiedFeedMock,
            $contentRepositoryMock
        );

        // Assume no header/body for mock request
        $mockUser = Mockery::mock();
        $mockUser->shouldReceive('getFirebaseId')->andReturn($firebaseId);
        Auth::shouldReceive('user')->andReturn($mockUser);
        $response = $service->execute();

        // assertion / check
        $this->assertInstanceOf(Collection::class, $response);
    }
}

