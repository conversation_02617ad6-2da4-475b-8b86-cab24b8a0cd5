<?php

namespace Tests\Unit\V4\Modules\Users\Services;

use App\V4\Modules\Users\Services\RetrievesUserDeviceProfileSummaries;
use App\V4\Modules\Users\ValueObjects\UserDeviceProfileSummary;
use Tests\TestCase;

class RetrievesUserDeviceProfileSummariesTest extends TestCase
{
    public function testItRetrievesUserDeviceProfileSummaries(): void
    {
        $fakeFirebaseId = $this->generator->uuid;
        $this->createAndroidProfile($fakeFirebaseId)->save();
        $this->createAndroidProfile($fakeFirebaseId)->save();

        $service = new RetrievesUserDeviceProfileSummaries();
        $profileSummaries = $service->execute($fakeFirebaseId);

        $this->assertCount(2, $profileSummaries);
        $this->assertInstanceOf(UserDeviceProfileSummary::class, $profileSummaries[0]);
    }
}
