<?php

namespace Tests\Unit\V4\Modules\Users\Logics;

use App\V4\Exceptions\UserNotFoundExceptionV4;
use App\V4\Models\UserLogin;
use App\V4\Modules\Users\Logics\RetrieveUserSummaryLogic;
use App\V4\Repositories\Publishers\PublisherRepositoryV4;
use App\V4\Repositories\UserLoginRepository;
use Tests\TestCase;
use Mockery;

class RetrieveUserSummaryLogicTest extends TestCase
{

    public function testItRetrieveUserSummaryLogic(): void {
        $providerUid = $this->generator->uuid;
        $fakeTotalFollowing = $this->generator->randomNumber(1);
        $fakeUserLogin = UserLogin::factory()->make();

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($providerUid)
            ->andReturn($fakeUserLogin);

        $publisherRepositoryV4Mock = Mockery::mock(PublisherRepositoryV4::class);
        $publisherRepositoryV4Mock
            ->shouldReceive('getTotalSubscribedForUser')
            ->once()
            ->with($providerUid)
            ->andReturn($fakeTotalFollowing);

        $logic = new RetrieveUserSummaryLogic(
            $userLoginRepositoryMock,
            $publisherRepositoryV4Mock
        );

        $logic->execute($providerUid);
    }

    public function testItThrowsUserNotFoundExceptionV4(): void {
        $this->expectException(UserNotFoundExceptionV4::class);
        $providerUid = $this->generator->uuid;

        $userLoginRepositoryMock = Mockery::mock(UserLoginRepository::class);
        $userLoginRepositoryMock
            ->shouldReceive('getUserLoginFromProviderUid')
            ->once()
            ->with($providerUid)
            ->andReturnNull();

        $publisherRepositoryV4Mock = Mockery::mock(PublisherRepositoryV4::class);
        $publisherRepositoryV4Mock->shouldNotReceive('getTotalSubscribedForUser');

        $logic = new RetrieveUserSummaryLogic(
            $userLoginRepositoryMock,
            $publisherRepositoryV4Mock
        );

        $logic->execute($providerUid);
    }
}
