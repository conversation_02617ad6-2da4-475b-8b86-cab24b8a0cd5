<?php

declare(strict_types=1);

namespace Tests\Traits;


use App\V4\Models\AndroidProfile;
use App\V4\Models\Profile;

trait TestUsers {
    public function createAndroidProfile(?string $firebaseId = null): AndroidProfile {
        $androidProfile = AndroidProfile::factory()->make();
        if ($firebaseId !== null) {
            $androidProfile->firebaseID = $firebaseId;
        }
        return $androidProfile;
    }

    public function createIosProfile(?string $firebaseId = null): Profile {
        $profile = Profile::factory()->make();
        if ($firebaseId !== null) {
            $profile->firebaseID = $firebaseId;
        }
        return $profile;
    }
}
