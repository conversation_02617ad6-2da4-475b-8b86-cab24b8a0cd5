<?php

$router->group(['prefix' => '/v1/internal', 'namespace' => 'Internal'], function($router) {
    $router->post('comments', 'InternalCommentControllerV4@createComment');
    $router->get('users/{firebaseId}', 'InternalUserControllerV4@getUserByFirebase');
    $router->post('users/publishers/following', 'InternalUserControllerV4@getUserPublishersFollowing');
    $router->get('users/profile/{firebaseId}', 'InternalUserControllerV4@getUserProfileByFirebaseId');
    $router->get('users/profile/{firebaseId}/{profileId}', 'InternalUserControllerV4@getUserProfile');
    $router->post('publishers/info', 'InternalPublishersControllerV4@getPublishersInfoByIds');
    $router->get('publishers/{publisherId}', 'InternalUserControllerV4@getByPublisherId');
    $router->post('users/bookmarks/{firebaseId}', 'InternalUserControllerV4@getBookmarkInfoByIds');


});

$router->group(['prefix' => '/v1/internal', 'middleware' => 'newswav.auth', 'namespace' => 'Internal'], function($router) {
    $router->post('live-video', 'InternalPostControllerV4@createLiveVideo');
    $router->put('live-video/{uniqueId}', 'InternalPostControllerV4@updateVideo');
    $router->get('videos', 'InternalPostControllerV4@getVideos');
    $router->get('videos/{id}', 'InternalPostControllerV4@getVideos');
    $router->get('ytpublishers', 'InternalPostControllerV4@getPublisherIds');
    $router->get('yttopics', 'InternalPostControllerV4@getAllTopics');
});
