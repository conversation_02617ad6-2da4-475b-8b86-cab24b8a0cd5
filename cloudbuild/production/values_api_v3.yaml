#DEPLOYMENT
fullnameOverride: newswav-v3-api
nameOverride: newswav-v3-api
replicaCount: 8
tier: backend
namespace: newswav

image:
  repository: asia-southeast1-docker.pkg.dev/bustling-sunset-220007/newswav-v3-api/newswav-v3-api
  tag: master

resources:
  limits:
    memory: 3200Mi
    cpu: 800m
  requests:
    memory: 1600Mi
    cpu: 400m

startupProbe:
  httpGet:
    path: /api/health/api
    port: 80
  failureThreshold: 12
  periodSeconds: 10
  timeoutSeconds: 5

readinessProbe:
  httpGet:
    path: /api/health/api
    port: 80
  initialDelaySeconds: 1
  periodSeconds: 5
  timeoutSeconds: 5

livenessProbe:
  httpGet:
      path: /api/health/api
      port: 80
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
  successThreshold: 1

nodeSelector:
  cloud.google.com/gke-spot: "true"
  
envFrom:
  - configMapRef:
      name: api-config-v3
  - secretRef:
      name: api-v3-gcpsm

appContainerPort: 80

#SERVICE
service:
  enabled: true
  type: NodePort
  port: 80

#HPA
autoscaling:
  enabled: true
  minReplicas: 8
  maxReplicas: 100
  targetCPUUtilizationPercentage: 40

#CONFIGMAP
configmap:
  enabled: true
  configmaps:
    - name: api-config-v3
      data:
        MYSQL_HOST: *************
        MYSQL_HOST_REPLICA: *************
        MYSQL_HOST_REPLICA_2: *************
        MYSQL_USERNAME: apiv3
        MYSQL_DB: newswav_news
        WP_TOKEN: whatever_would_work!
        APP_NAME: newswav-v3-api
        APP_DEBUG: "false"
        APP_URL: https://newswav.com
        API_URL: https://api.newswav.com
        WEBSITE_URL: https://newswav.com
        APP_TIMEZONE: Asia/Kuala_Lumpur
        CACHE_DRIVER: file
        QUEUE_CONNECTION: sync
        LOG_CHANNEL: stack
        DB_CONNECTION: mysql
        DB_PORT: "3306"
        APP_ENV: production
        GEMINI_API_LOCATION: us-central1
        GCP_CLIENT_EVENTS_TOPIC: track_events_client_side_prod
        GCP_CLIENT_EVENTS_TOPIC_V2: track_events_client_side_prod_v2
        GCP_SERVER_EVENTS_TOPIC: track_events_server_side_prod
        REDIS_FEED_HOST: ************
        REDIS_FEED_DB: "0"
        REDIS_FEED_READ_TIMEOUT: "5"
        REDIS_HOST: **********
        REDIS_DB: "0"
        REDIS_READ_TIMEOUT: "5"
        KUBE_MASTER_HOST: https://************
        KUBE_API_V3: hpa-api-v3-autoscaler
        KUBE_API_TRACK: hpa-api-v3-autoscaler-track
        KUBE_API: hpa-autoscaler
        KUBE_NAMESPACE: newswav
        KUBE_JOBS_MASTER_HOST: https://*************
        KUBE_JOBS_NAMESPACE: newswav-jobs-only
        KUBE_V3_MASTER_HOST: "https://************"
        KUBE_V3_NAMESPACE: "newswav"
        KUBE_V3_AUTOSCALER: "newswav-v3-api-hpa"
        KUBE_V3_TRACK_AUTOSCALER: "newswav-v3-api-track-hpa"
        KUBE_FEED_AUTOSCALER: "feed-api-hpa"
        FIREBASE_PROJECT_ID: firebase-newswav
        BUGSNAG_LOGGER_LEVEL: critical
        ELASTIC_SEARCH_URL: ***********
        ELASTIC_PORT: "9200"
        ES_ARTICLES_INDEX: articles_production
        ES_TOPICS_INDEX: topics_production
        ES_PUBLISHERS_INDEX: publishers_production
        ES_COMMENTS_INDEX: comments_production
        ES_VIDEOS_INDEX: videos_production
        ES_PODCASTS_INDEX: podcasts_production
        LOG_SLACK_WEBHOOK_URL: *******************************************************************************
        CORONA-CARD-API-KEY: 2WnaRSi4F0TSdksx
        DELAYED_TRACK_EVENTS_INSERT: "true"
        PN_CLUSTER_DB_PORT: "3310"
        TRACK_CLUSTER_DB_PORT: "3311"
        SWOOLE_HTTP_REACTOR_NUM: "8"
        SWOOLE_HTTP_TASK_WORKER_NUM: "8"
        SWOOLE_HTTP_WORKER_NUM: "8"
        MAILCHIMP_LIST_ID: 836a353cae
        MAIL_DRIVER: smtp
        MAIL_USERNAME: <EMAIL>

        ADMIN_API: https://admin.newswav.com
        ADMIN_TIMEOUT: "5"
        PROJECT_HOST: newswav.com
        CRAWLER_SLACK: *******************************************************************************
        IMAGE_PROXY_QUALITY: "50"
        IMAGE_PROXY_SQUARE: "500"
        IMAGE_PROXY_WIDTH: "800"
        IMAGE_PROXY_HEIGHT: "450"
        CDN_HOST: https://cdn.newswav.com/
        GCP_CDN_BUCKET: cdn.newswav.com
        SHOW_BOOKMARK_FLAG: "TRUE"
        SHOW_HISTORY_FLAG: "TRUE"
        USER_DATA_SERVICE_API_HOST: http://nitro-user-data-svc-service.newswav.svc.cluster.local
        FEEDBACK_EMAIL_RECEIVER: <EMAIL>
        PARTNER_EMAIL_RECEIVER: <EMAIL>
        LOKALWAV_SLACK: *******************************************************************************
        UPTIME_HOST: https://uptime-monitoring.newswav.com

        MYSQL_HOST_ADWAV: ************
        MYSQL_PN_DB: newswav_pn
        MYSQL_TRACK_DB: newswav_track
        MYSQL_ADMIN_DB: newswav_admin
        PN_DB_PORT: "3306" 
        TRACK_DB_PORT: "3306"
        ADMIN_DB_PORT: "3306"
        MYSQL_ADMIN_USERNAME: newswav-admin-api
        MYSQL_HOST_PROXYSQL_ADWAV: proxysql-adwav-db-svc.proxysql.svc.cluster.local
        PROXYSQL_DB_PORT: "6033"

        COMMENT_SVC_HOST: comment-api-service.newswav.svc.cluster.local:3000

        GOOGLE_CLOUD_PROJECT_ID: bustling-sunset-220007
        WEB_USER_DATA_SERVICE_API_HOST: http://web-anonymous-service.newswav.svc.cluster.local:3000/
        OUTBOX_HOST: "http://**********:80"
        OUTBOX_PATH: "/outbox/api/event"
        
        KAFKA_SECURITY_PROTOCOL: SASL_SSL
        KAFKA_FEED_CONTENT_EVENTS_TOPIC: content-actions
        KAFKA_USER_EVENTS_TOPIC: user-actions
        KAFKA_COMMENT_EVENTS_TOPIC: comment-service-actions
        SLACK_SOCMED_CHANNEL_WEBHOOK: ******************************************************************************* ## socmed (private channel)
        FEED_SVC_HOST: http://feed-api-service.newswav.svc.cluster.local:3000
        SLACK_BACKEND_NOTIFY_WEBHOOK: *******************************************************************************
        OUTBOX_AUTH_HEADER: "X-NW-OUTBOX"

        PUSHER_APP_ID: "*******"
        PUSHER_APP_KEY: "dd2525e04cc671c9f074"
        PUSHER_APP_SECRET: "d11d5a35aed7805afa10"
        PUSHER_HOST: "soketi-************.asia-southeast1.run.app"
        ELECTION_SERVICE_URL: "https://election-service-************.asia-southeast1.run.app"

serviceAccount:
  create: true
  annotations: 
        iam.gke.io/gcp-service-account: <EMAIL>
  name: newswav-v3-api-sa
