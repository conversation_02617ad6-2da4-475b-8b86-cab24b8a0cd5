APP_ENV: "staging"
APP_NAME: "newswav-api-v3"
APP_DEBUG: "true"
APP_URL: "https://dev-newswav-api.newswav.dev"
API_URL: "https://dev-newswav-api.newswav.dev"
WEBSITE_URL: "https://dev-website.newswav.dev"
APP_TIMEZONE: "Asia/Kuala_Lumpur"
CACHE_DRIVER: "file"
QUEUE_CONNECTION: "sync"
LOG_CHANNEL: "stack"
BUGSNAG_LOGGER_LEVEL: "critical"
WP_TOKEN: "whatever_would_work!"
GCP_CLIENT_EVENTS_TOPIC: "track_events_client_side_prod"
GCP_CLIENT_EVENTS_TOPIC_V2: "track_events_client_side_prod_v2"
GCP_SERVER_EVENTS_TOPIC: "track_events_server_side_prod"
GCP_CDN_BUCKET: "cdn.newswav.dev"
CDN_HOST: "https://cdn.newswav.dev/"
JOB_TEMPLATE_BUCKET: "nw-kube-jobs-templates-dev"
LOG_SLACK_WEBHOOK_URL: "*****************************************************************************"
CRAWLER_SLACK: "*******************************************************************************"
MYSQL_HOST: "**********"
MYSQL_USERNAME: "newswav-v3-api"
MYSQL_DB: "newswav_news"
DB_CONNECTION: "mysql"
DB_PORT: "3306"
MYSQL_HUAWEI_HOST: "**********"
MYSQL_HUAWEI_DB: "newswav_campaign"
MYSQL_HUAWEI_USERNAME: "newswav-v3-api"
MYSQL_ELECTION_HOST: "**********"
MYSQL_ELECTION_DB: "newswav_admin"
MYSQL_ELECTION_USERNAME: "newswav-v3-api"
MYSQL_PROMO_HOST: "**********"
MYSQL_PROMO_DB: "newswav_app"
MYSQL_PROMO_USERNAME: "newswav-v3-api"
MYSQL_POOL_DB: "newswav_poll"
POLL_CLUSTER_DB_PORT: "3306"
MYSQL_ADMIN_DB: "newswav_admin"
PN_CLUSTER_DB: "newswav_news_pn"
PN_CLUSTER_DB_PORT: "3306"
TRACK_CLUSTER_DB: "newswav_news_track"
TRACK_CLUSTER_DB_PORT: "3306"
ADMIN_API: "dev-newswav-admin-api.newswav.dev"
PROJECT_HOST: "newswav.dev"
REDIS_HOST: "**********"
REDIS_DB: "0"
REDIS_READ_TIMEOUT: "5"
REDIS_FEED_HOST: "**********"
REDIS_FEED_DB: "0"
REDIS_FEED_READ_TIMEOUT: "5"
ELASTIC_SEARCH_URL: "***********"
ELASTIC_PORT: "9200"
ES_ARTICLES_INDEX: "articles_production"
ES_TOPICS_INDEX: "topics_production"
ES_PUBLISHERS_INDEX: "publishers_production"
ES_COMMENTS_INDEX: "comments_production"
ES_VIDEOS_INDEX: "videos_production"
ES_PODCASTS_INDEX: "podcasts_production"
KUBE_JOBS_MASTER_HOST: "https://**************"
KUBE_JOBS_NAMESPACE: "stag-kns-nw-static-job"
SECRET_PN_ADMIN: "5142b1bf4df0aea9a0ad0ece3d56ed1a60e1d936"
SECRET_PN_TEST: "a72f009efa2b76651bd9cfe48159d6f6e01bcf98"
FIREBASE_PROJECT_ID: "newswav-dev"
STACKDRIVER_ENABLED: "false"
CORONA-CARD-API-KEY: 2WnaRSi4F0TSdksx
DELAYED_TRACK_EVENTS_INSERT: "true"
SWOOLE_HTTP_REACTOR_NUM: "16"
SWOOLE_HTTP_TASK_WORKER_NUM: "16"
SWOOLE_HTTP_WORKER_NUM: "16"
MAILCHIMP_LIST_ID: "836a353cae"
IMAGE_PROXY_QUALITY: "50"
IMAGE_PROXY_SQUARE: "500"
IMAGE_PROXY_WIDTH: "800"
IMAGE_PROXY_HEIGHT: "450"
POLL_CHANNEL_EN: "1109"
POLL_CHANNEL_ZH: "1110"
POLL_CHANNEL_MS: "1111"
AWS_ACCESS_KEY_ID: "AKIASRBBUEH6DIGY6UNI"
FEEDBACK_EMAIL_RECEIVER: "<EMAIL>"
PARTNER_EMAIL_RECEIVER: "<EMAIL>"
USER_DATA_SERVICE_API_KEY: "8R9q3vmjkCSmFkpt"
USER_DATA_SERVICE_API_HOST: "https://dev-user-data-svc.newswav.dev"
AWS_S3_ACCESS_KEY_ID: "AKIASRBBUEH6OUC7THWM"
LOKALWAV_SLACK: "*******************************************************************************"
UPTIME_HOST: "https://dev-uptime-monitoring.newswav.dev"
REQUEST_DEBUG: "false"
COMMENT_SVC_HOST: "https://dev-comment-svc-api.newswav.dev"
MYSQL_PN_DB: "newswav_news_pn"
MYSQL_TRACK_DB: "newswav_track"
PN_DB_PORT: "3306"
TRACK_DB_PORT: "3306"
ADMIN_DB_PORT: "3306"
GOOGLE_CLOUD_PROJECT_ID: "nw-development-329802"
WEB_USER_DATA_SERVICE_API_HOST: "https://dev-web-anon.newswav.dev"
FEED_SVC_HOST: "http://feed-api-service.stag-kns-nw-static.svc.cluster.local:3000"
KAFKA_SECURITY_PROTOCOL: "SASL_SSL"
KAFKA_FEED_CONTENT_EVENTS_TOPIC: "dev-content-actions"
KAFKA_USER_EVENTS_TOPIC: "dev-user-actions"
SLACK_SOCMED_CHANNEL_WEBHOOK: "*******************************************************************************"
OUTBOX_HOST: "http://**********:8080"
OUTBOX_PATH: "/outbox/api/event"
MYSQL_HOST_PROXYSQL_ADWAV: "**********"
PROXYSQL_DB_PORT: "3306"
OUTBOX_AUTH_HEADER: "X-NW-OUTBOX"
KUBE_V3_MASTER_HOST: "https://************"
KUBE_V3_NAMESPACE: "newswav"
KUBE_V3_AUTOSCALER: "newswav-v3-api-hpa"
KUBE_V3_TRACK_AUTOSCALER: "newswav-v3-api-track-hpa"
KUBE_FEED_AUTOSCALER: "feed-api-hpa"
UGC_API_SECRET: "TJSXSzm6e563ZAvC"

PUSHER_APP_ID: "*******"
PUSHER_APP_KEY: "7206efd1a6899ea33994"
PUSHER_APP_SECRET: "5f4786c1ba4ea31a44f3"
PUSHER_HOST: "soketi-1008207427045.asia-southeast1.run.app"
ELECTION_SERVICE_URL: "https://election-service-1008207427045.asia-southeast1.run.app"
